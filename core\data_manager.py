"""
High-performance data manager for Binance historical and real-time data.
Optimized for speed and memory efficiency.
"""
import asyncio
import sqlite3
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
# Try to import Binance client, fallback if not available
try:
    from binance.client import Client
    from binance.exceptions import BinanceAPIException
    BINANCE_AVAILABLE = True
except ImportError:
    BINANCE_AVAILABLE = False
    Client = None
    BinanceAPIException = Exception
# Redis disabled - using only API and local storage
REDIS_AVAILABLE = False
redis = None

import pickle
from numba import jit
from loguru import logger
from config.settings import settings


class DataManager:
    """Ultra-fast data manager with caching and optimization."""

    def __init__(self):
        # Initialize Binance client if available
        if BINANCE_AVAILABLE:
            try:
                self.client = Client(
                    settings.binance_api_key,
                    settings.binance_api_secret,
                    testnet=settings.binance_testnet
                )
            except Exception as e:
                logger.warning(f"Failed to initialize Binance client: {e}")
                self.client = None
        else:
            logger.warning("Binance client not available - using demo data")
            self.client = None

        # Redis disabled - no cache
        self.redis_client = None
        self.db_path = settings.database_url.replace("sqlite:///", "")
        self._init_database()

    def _init_database(self):
        """Initialize SQLite database with optimized schema."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("""
            CREATE TABLE IF NOT EXISTS klines (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                interval TEXT NOT NULL,
                open_time INTEGER NOT NULL,
                open REAL NOT NULL,
                high REAL NOT NULL,
                low REAL NOT NULL,
                close REAL NOT NULL,
                volume REAL NOT NULL,
                close_time INTEGER NOT NULL,
                quote_asset_volume REAL,
                number_of_trades INTEGER,
                taker_buy_base_asset_volume REAL,
                taker_buy_quote_asset_volume REAL,
                UNIQUE(symbol, interval, open_time)
            )
        """)

        # Create indexes for fast queries
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_symbol_interval_time
            ON klines(symbol, interval, open_time)
        """)

        conn.commit()
        conn.close()

    def get_historical_data(
        self,
        symbol: str,
        interval: str,
        start_date: str,
        end_date: str,
        force_update: bool = False
    ) -> pd.DataFrame:
        """
        Get historical kline data with intelligent caching.

        Args:
            symbol: Trading pair symbol (e.g., 'BTCUSDT')
            interval: Kline interval (e.g., '1h', '1d')
            start_date: Start date in 'YYYY-MM-DD' format
            end_date: End date in 'YYYY-MM-DD' format
            force_update: Force data refresh from API

        Returns:
            DataFrame with OHLCV data
        """
        cache_key = f"klines:{symbol}:{interval}:{start_date}:{end_date}"

        # Try cache first
        if not force_update and self.redis_client:
            cached_data = self.redis_client.get(cache_key)
            if cached_data:
                logger.info(f"Loading {symbol} data from cache")
                return pickle.loads(cached_data)

        # Check database
        df = self._load_from_database(symbol, interval, start_date, end_date)

        if df.empty or force_update:
            logger.info(f"Fetching {symbol} data from Binance API")
            df = self._fetch_from_api(symbol, interval, start_date, end_date)
            self._save_to_database(df, symbol, interval)

        # Cache the result
        if self.redis_client:
            self.redis_client.setex(
                cache_key,
                3600,  # 1 hour cache
                pickle.dumps(df)
            )

        return self._optimize_dataframe(df)

    def _load_from_database(
        self,
        symbol: str,
        interval: str,
        start_date: str,
        end_date: str
    ) -> pd.DataFrame:
        """Load data from SQLite database."""
        start_timestamp = int(pd.Timestamp(start_date).timestamp() * 1000)
        end_timestamp = int(pd.Timestamp(end_date).timestamp() * 1000)

        query = """
            SELECT open_time, open, high, low, close, volume
            FROM klines
            WHERE symbol = ? AND interval = ?
            AND open_time >= ? AND open_time <= ?
            ORDER BY open_time
        """

        conn = sqlite3.connect(self.db_path)
        df = pd.read_sql_query(
            query,
            conn,
            params=(symbol, interval, start_timestamp, end_timestamp)
        )
        conn.close()

        if not df.empty:
            df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
            df.set_index('open_time', inplace=True)

        return df

    def _fetch_from_api(
        self,
        symbol: str,
        interval: str,
        start_date: str,
        end_date: str
    ) -> pd.DataFrame:
        """Fetch data from Binance API with error handling."""

        # If no client available, generate demo data
        if not self.client:
            logger.info(f"Generating demo data for {symbol}")
            return self._generate_demo_data(symbol, interval, start_date, end_date)

        try:
            klines = self.client.get_historical_klines(
                symbol,
                interval,
                start_date,
                end_date
            )

            if not klines:
                logger.warning(f"No data returned from API for {symbol}, generating demo data")
                return self._generate_demo_data(symbol, interval, start_date, end_date)

            df = pd.DataFrame(klines, columns=[
                'open_time', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume',
                'ignore'
            ])

            # Convert data types for performance
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            df[numeric_columns] = df[numeric_columns].astype(float)
            df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
            df['close_time'] = pd.to_datetime(df['close_time'], unit='ms')  # Fix close_time
            df.set_index('open_time', inplace=True)

            return df[numeric_columns]

        except BinanceAPIException as e:
            logger.error(f"Binance API error: {e}, falling back to demo data")
            return self._generate_demo_data(symbol, interval, start_date, end_date)
        except Exception as e:
            logger.error(f"Unexpected error fetching data: {e}, falling back to demo data")
            return self._generate_demo_data(symbol, interval, start_date, end_date)

    def _generate_demo_data(
        self,
        symbol: str,
        interval: str,
        start_date: str,
        end_date: str
    ) -> pd.DataFrame:
        """Generate realistic demo data for testing."""

        # Parse dates
        start = pd.Timestamp(start_date)
        end = pd.Timestamp(end_date)

        # Determine frequency based on interval
        freq_map = {
            '1m': '1T', '3m': '3T', '5m': '5T', '15m': '15T', '30m': '30T',
            '1h': '1H', '2h': '2H', '4h': '4H', '6h': '6H', '8h': '8H', '12h': '12H',
            '1d': '1D', '3d': '3D', '1w': '1W', '1M': '1M'
        }

        freq = freq_map.get(interval, '1H')

        # Generate time index
        time_index = pd.date_range(start=start, end=end, freq=freq)

        if len(time_index) == 0:
            return pd.DataFrame()

        # Base price for different symbols
        base_prices = {
            'BTCUSDT': 45000,
            'ETHUSDT': 3000,
            'BNBUSDT': 400,
            'ADAUSDT': 0.5,
            'SOLUSDT': 100,
            'DOTUSDT': 25,
            'LINKUSDT': 15,
            'MATICUSDT': 1.2
        }

        base_price = base_prices.get(symbol, 100)

        # Generate realistic price movements
        np.random.seed(hash(symbol) % 2**32)  # Consistent seed per symbol

        # Generate returns with some trend and volatility clustering
        n_points = len(time_index)
        returns = np.random.normal(0.0001, 0.02, n_points)  # Small positive drift

        # Add volatility clustering
        volatility = np.random.normal(0.02, 0.005, n_points)
        volatility = np.abs(volatility)
        returns = returns * volatility

        # Generate prices
        prices = [base_price]
        for i in range(1, n_points):
            new_price = prices[-1] * (1 + returns[i])
            prices.append(max(new_price, 0.01))  # Ensure positive prices

        # Generate OHLC data
        data = []
        for i, price in enumerate(prices):
            # Generate realistic OHLC around the close price
            volatility_factor = np.random.uniform(0.005, 0.02)

            high = price * (1 + volatility_factor * np.random.uniform(0.2, 1.0))
            low = price * (1 - volatility_factor * np.random.uniform(0.2, 1.0))

            if i == 0:
                open_price = price
            else:
                open_price = prices[i-1] * (1 + np.random.normal(0, 0.001))

            # Ensure OHLC consistency
            high = max(high, open_price, price)
            low = min(low, open_price, price)

            # Generate volume
            volume = np.random.uniform(1000, 10000)

            data.append({
                'open': open_price,
                'high': high,
                'low': low,
                'close': price,
                'volume': volume
            })

        df = pd.DataFrame(data, index=time_index)

        logger.info(f"Generated {len(df)} demo data points for {symbol}")
        return df

    def _save_to_database(self, df: pd.DataFrame, symbol: str, interval: str):
        """Save data to SQLite database."""
        if df.empty:
            return

        conn = sqlite3.connect(self.db_path)

        for timestamp, row in df.iterrows():
            try:
                # Calculate close_time based on interval
                open_time_ms = int(timestamp.timestamp() * 1000)

                # Map intervals to milliseconds
                interval_ms = {
                    '1m': 60 * 1000,
                    '3m': 3 * 60 * 1000,
                    '5m': 5 * 60 * 1000,
                    '15m': 15 * 60 * 1000,
                    '30m': 30 * 60 * 1000,
                    '1h': 60 * 60 * 1000,
                    '2h': 2 * 60 * 60 * 1000,
                    '4h': 4 * 60 * 60 * 1000,
                    '6h': 6 * 60 * 60 * 1000,
                    '8h': 8 * 60 * 60 * 1000,
                    '12h': 12 * 60 * 60 * 1000,
                    '1d': 24 * 60 * 60 * 1000,
                    '3d': 3 * 24 * 60 * 60 * 1000,
                    '1w': 7 * 24 * 60 * 60 * 1000,
                    '1M': 30 * 24 * 60 * 60 * 1000
                }

                close_time_ms = open_time_ms + interval_ms.get(interval, 60 * 60 * 1000)

                conn.execute("""
                    INSERT OR REPLACE INTO klines
                    (symbol, interval, open_time, open, high, low, close, volume, close_time)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    symbol, interval, open_time_ms,
                    row['open'], row['high'], row['low'], row['close'], row['volume'],
                    close_time_ms
                ))
            except Exception as e:
                logger.warning(f"Error saving data point: {e}")

        conn.commit()
        conn.close()

    @staticmethod
    @jit(nopython=True)
    def _calculate_returns(prices: np.ndarray) -> np.ndarray:
        """JIT-compiled function for fast return calculation."""
        returns = np.zeros(len(prices))
        for i in range(1, len(prices)):
            returns[i] = (prices[i] - prices[i-1]) / prices[i-1]
        return returns

    def _optimize_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Optimize DataFrame for memory and performance."""
        if df.empty:
            return df

        # Add technical indicators that are commonly used
        df['returns'] = self._calculate_returns(df['close'].values)
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        df['volatility'] = df['returns'].rolling(20).std()

        # Optimize data types
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = pd.to_numeric(df[col], downcast='float')

        return df

    def get_latest_price(self, symbol: str) -> float:
        """Get latest price for a symbol."""
        try:
            ticker = self.client.get_symbol_ticker(symbol=symbol)
            return float(ticker['price'])
        except Exception as e:
            logger.error(f"Error getting latest price for {symbol}: {e}")
            return 0.0

    def get_multiple_symbols_data(
        self,
        symbols: List[str],
        interval: str,
        start_date: str,
        end_date: str
    ) -> Dict[str, pd.DataFrame]:
        """Get data for multiple symbols efficiently."""
        data = {}

        if settings.parallel_processing:
            # Use asyncio for concurrent API calls
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            async def fetch_symbol_data(symbol):
                return symbol, self.get_historical_data(symbol, interval, start_date, end_date)

            async def fetch_all():
                tasks = [fetch_symbol_data(symbol) for symbol in symbols]
                results = await asyncio.gather(*tasks)
                return dict(results)

            data = loop.run_until_complete(fetch_all())
            loop.close()
        else:
            for symbol in symbols:
                data[symbol] = self.get_historical_data(symbol, interval, start_date, end_date)

        return data
