"""
Advanced Evolution Engine for iterative strategy improvement.
Implements robust validation, real-world testing, and continuous optimization.
"""
import numpy as np
import pandas as pd
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass, asdict
from concurrent.futures import <PERSON><PERSON>oolExecutor, as_completed
import sqlite3
from loguru import logger

from core.backtester import VectorizedBacktester, BacktestResult
from core.optimizer import StrategyOptimizer
from core.data_manager import DataManager
from utils.metrics import AdvancedMetrics
from utils.performance_tracker import PerformanceTracker


@dataclass
class EvolutionGeneration:
    """Represents one generation in the evolution process."""
    generation: int
    timestamp: datetime
    strategies: List[Dict[str, Any]]
    best_strategy: Dict[str, Any]
    population_stats: Dict[str, float]
    improvements: Dict[str, float]
    validation_results: Dict[str, Any]


@dataclass
class RobustnessMetrics:
    """Comprehensive robustness validation metrics."""
    walk_forward_consistency: float
    out_of_sample_performance: float
    market_regime_stability: float
    parameter_sensitivity: float
    drawdown_recovery: float
    tail_risk_management: float
    live_simulation_score: float
    overall_robustness: float


class EvolutionEngine:
    """
    Advanced evolution engine for iterative strategy improvement.
    Implements real-world validation and robust metric calculation.
    """

    def __init__(
        self,
        data_manager: DataManager,
        initial_capital: float = 100000,
        evolution_generations: int = 50,
        population_size: int = 20,
        elite_ratio: float = 0.2,
        mutation_rate: float = 0.3
    ):
        self.data_manager = data_manager
        self.backtester = VectorizedBacktester(initial_capital)
        self.optimizer = StrategyOptimizer(data_manager, self.backtester)
        self.performance_tracker = PerformanceTracker()

        # Evolution parameters
        self.evolution_generations = evolution_generations
        self.population_size = population_size
        self.elite_ratio = elite_ratio
        self.mutation_rate = mutation_rate

        # Validation settings
        self.validation_periods = 12  # 12 months of walk-forward
        self.out_of_sample_ratio = 0.3  # 30% for out-of-sample testing
        self.min_trades_threshold = 30  # Minimum trades for valid strategy

        # Evolution history
        self.generations: List[EvolutionGeneration] = []
        self.best_ever_strategy = None
        self.best_ever_score = -float('inf')

        logger.info("Evolution Engine initialized")

    def evolve_strategies(
        self,
        strategy_classes: List[type],
        symbols: List[str],
        timeframes: List[str],
        start_date: str,
        end_date: str
    ) -> Dict[str, Any]:
        """
        Main evolution loop - iteratively improve strategies.

        Args:
            strategy_classes: List of strategy classes to evolve
            symbols: List of trading symbols
            timeframes: List of timeframes
            start_date: Evolution start date
            end_date: Evolution end date

        Returns:
            Evolution results with best strategies and metrics
        """
        logger.info(f"🧬 Starting evolution with {len(strategy_classes)} strategy types")
        logger.info(f"   Symbols: {symbols}")
        logger.info(f"   Timeframes: {timeframes}")
        logger.info(f"   Generations: {self.evolution_generations}")

        start_time = time.time()

        # Initialize population
        population = self._initialize_population(
            strategy_classes, symbols, timeframes, start_date, end_date
        )

        logger.info(f"✅ Initialized population with {len(population)} strategies")

        # Evolution loop
        for generation in range(self.evolution_generations):
            logger.info(f"\n🔄 Generation {generation + 1}/{self.evolution_generations}")

            # Evaluate population
            evaluated_population = self._evaluate_population(
                population, start_date, end_date
            )

            # Calculate robustness metrics
            robust_population = self._calculate_robustness_metrics(
                evaluated_population, start_date, end_date
            )

            # Select best strategies
            elite_strategies = self._select_elite(robust_population)

            # Track generation
            generation_data = self._create_generation_record(
                generation, robust_population, elite_strategies
            )
            self.generations.append(generation_data)

            # Log progress
            best_current = elite_strategies[0]
            logger.info(f"   🏆 Best strategy: {best_current['name']}")
            logger.info(f"   📊 Fitness score: {best_current['fitness_score']:.4f}")
            logger.info(f"   🛡️ Robustness: {best_current['robustness_score']:.4f}")
            logger.info(f"   📈 Return: {best_current['total_return']:.2%}")
            logger.info(f"   📉 Max DD: {best_current['max_drawdown']:.2%}")

            # Check for new best ever
            if best_current['fitness_score'] > self.best_ever_score:
                self.best_ever_strategy = best_current.copy()
                self.best_ever_score = best_current['fitness_score']
                logger.info(f"   🎉 NEW BEST EVER STRATEGY!")

            # Early stopping if converged
            if self._check_convergence():
                logger.info(f"   ⏹️ Convergence detected, stopping early")
                break

            # Generate next population
            if generation < self.evolution_generations - 1:
                population = self._generate_next_population(elite_strategies)

        total_time = time.time() - start_time

        # Final validation and results
        final_results = self._generate_final_results(total_time)

        logger.info(f"\n🏁 Evolution completed in {total_time/60:.1f} minutes")
        logger.info(f"🏆 Best strategy achieved {self.best_ever_score:.4f} fitness score")

        return final_results

    def _initialize_population(
        self,
        strategy_classes: List[type],
        symbols: List[str],
        timeframes: List[str],
        start_date: str,
        end_date: str
    ) -> List[Dict[str, Any]]:
        """Initialize the first generation population."""
        population = []

        strategies_per_class = max(1, self.population_size // len(strategy_classes))

        for strategy_class in strategy_classes:
            for symbol in symbols:
                for timeframe in timeframes:
                    for i in range(strategies_per_class):
                        # Generate random parameters
                        params = self._generate_random_parameters(strategy_class)

                        strategy_config = {
                            'class': strategy_class,
                            'symbol': symbol,
                            'timeframe': timeframe,
                            'parameters': params,
                            'name': f"{strategy_class.__name__}_{symbol}_{timeframe}_{i}",
                            'generation': 0
                        }

                        population.append(strategy_config)

                        if len(population) >= self.population_size:
                            break
                    if len(population) >= self.population_size:
                        break
                if len(population) >= self.population_size:
                    break
            if len(population) >= self.population_size:
                break

        return population[:self.population_size]

    def _generate_random_parameters(self, strategy_class: type) -> Dict[str, Any]:
        """Generate random parameters for a strategy class."""
        # Define parameter ranges for each strategy type
        param_ranges = {
            'MovingAverageCrossover': {
                'fast_period': (5, 20),
                'slow_period': (20, 50),
                'trend_period': (50, 200),
                'min_trend_strength': (0.01, 0.05)
            },
            'RSIMeanReversion': {
                'rsi_period': (10, 25),
                'oversold_threshold': (20, 35),
                'overbought_threshold': (65, 80),
                'trend_period': (30, 100)
            },
            'BollingerBandsMomentum': {
                'bb_period': (15, 30),
                'bb_std': (1.5, 2.5),
                'volume_threshold': (1.2, 2.5),
                'momentum_period': (5, 20)
            },
            'MACDStrategy': {
                'fast_period': (8, 16),
                'slow_period': (20, 35),
                'signal_period': (6, 15),
                'histogram_threshold': (0.0, 0.001)
            }
        }

        ranges = param_ranges.get(strategy_class.__name__, {})
        params = {}

        for param_name, (min_val, max_val) in ranges.items():
            if isinstance(min_val, int):
                params[param_name] = np.random.randint(min_val, max_val + 1)
            else:
                params[param_name] = np.random.uniform(min_val, max_val)

        return params

    def _evaluate_population(
        self,
        population: List[Dict[str, Any]],
        start_date: str,
        end_date: str
    ) -> List[Dict[str, Any]]:
        """Evaluate all strategies in the population."""
        logger.info(f"   📊 Evaluating {len(population)} strategies...")

        evaluated_population = []

        # Use sequential evaluation to avoid multiprocessing issues
        for strategy_config in population:
            try:
                result = self._evaluate_single_strategy(
                    strategy_config,
                    start_date,
                    end_date
                )
                if result:
                    evaluated_population.append(result)
            except Exception as e:
                logger.warning(f"Strategy evaluation failed: {e}")
                continue

        logger.info(f"   ✅ Successfully evaluated {len(evaluated_population)} strategies")
        return evaluated_population

    def _evaluate_single_strategy(
        self,
        strategy_config: Dict[str, Any],
        start_date: str,
        end_date: str
    ) -> Optional[Dict[str, Any]]:
        """Evaluate a single strategy."""
        try:
            # Get data
            data = self.data_manager.get_historical_data(
                strategy_config['symbol'],
                strategy_config['timeframe'],
                start_date,
                end_date
            )

            if data.empty or len(data) < 1000:
                return None

            # Create strategy instance
            strategy = strategy_config['class'](
                strategy_config['symbol'],
                strategy_config['timeframe'],
                **strategy_config['parameters']
            )

            # Run backtest
            result = self.backtester.run_backtest(strategy, data)

            # Calculate fitness score
            fitness_score = self._calculate_fitness_score(result)

            # Add evaluation results to config
            strategy_config.update({
                'backtest_result': result,
                'fitness_score': fitness_score,
                'total_return': result.total_return,
                'sharpe_ratio': result.sharpe_ratio,
                'max_drawdown': result.max_drawdown,
                'win_rate': result.win_rate,
                'total_trades': result.total_trades,
                'evaluation_time': datetime.now()
            })

            return strategy_config

        except Exception as e:
            logger.debug(f"Strategy evaluation error: {e}")
            return None

    def _calculate_fitness_score(self, result: BacktestResult) -> float:
        """
        Calculate comprehensive fitness score.
        Combines multiple metrics for robust evaluation.
        """
        # Base metrics
        sharpe_ratio = max(result.sharpe_ratio, -3)  # Cap at -3
        total_return = result.total_return
        max_drawdown = result.max_drawdown
        win_rate = result.win_rate

        # Penalty for insufficient trades
        trade_penalty = 1.0
        if result.total_trades < self.min_trades_threshold:
            trade_penalty = result.total_trades / self.min_trades_threshold

        # Composite fitness score
        fitness_score = (
            sharpe_ratio * 0.4 +  # Risk-adjusted returns (40%)
            (total_return * 2) * 0.3 +  # Total returns (30%)
            (1 - max_drawdown) * 0.2 +  # Drawdown control (20%)
            win_rate * 0.1  # Win rate (10%)
        ) * trade_penalty

        return max(fitness_score, -10)  # Floor at -10

    def _calculate_robustness_metrics(
        self,
        population: List[Dict[str, Any]],
        start_date: str,
        end_date: str
    ) -> List[Dict[str, Any]]:
        """Calculate comprehensive robustness metrics for each strategy."""
        logger.info(f"   🛡️ Calculating robustness metrics...")

        for strategy_config in population:
            try:
                robustness = self._calculate_single_robustness(
                    strategy_config, start_date, end_date
                )
                strategy_config['robustness_metrics'] = robustness
                strategy_config['robustness_score'] = robustness.overall_robustness

                # Update fitness score with robustness
                original_fitness = strategy_config['fitness_score']
                strategy_config['fitness_score'] = (
                    original_fitness * 0.7 + robustness.overall_robustness * 0.3
                )

            except Exception as e:
                logger.debug(f"Robustness calculation failed: {e}")
                strategy_config['robustness_score'] = 0.0

        return population

    def _calculate_single_robustness(
        self,
        strategy_config: Dict[str, Any],
        start_date: str,
        end_date: str
    ) -> RobustnessMetrics:
        """Calculate robustness metrics for a single strategy."""

        # Get extended data for robustness testing
        data = self.data_manager.get_historical_data(
            strategy_config['symbol'],
            strategy_config['timeframe'],
            start_date,
            end_date
        )

        if data.empty:
            return RobustnessMetrics(0, 0, 0, 0, 0, 0, 0, 0)

        # 1. Walk-forward consistency
        wf_consistency = self._test_walk_forward_consistency(strategy_config, data)

        # 2. Out-of-sample performance
        oos_performance = self._test_out_of_sample_performance(strategy_config, data)

        # 3. Market regime stability
        regime_stability = self._test_market_regime_stability(strategy_config, data)

        # 4. Parameter sensitivity
        param_sensitivity = self._test_parameter_sensitivity(strategy_config, data)

        # 5. Drawdown recovery
        dd_recovery = self._test_drawdown_recovery(strategy_config, data)

        # 6. Tail risk management
        tail_risk = self._test_tail_risk_management(strategy_config, data)

        # 7. Live simulation score (simplified)
        live_sim_score = min(wf_consistency, oos_performance)

        # Overall robustness score
        overall_robustness = np.mean([
            wf_consistency, oos_performance, regime_stability,
            param_sensitivity, dd_recovery, tail_risk, live_sim_score
        ])

        return RobustnessMetrics(
            walk_forward_consistency=wf_consistency,
            out_of_sample_performance=oos_performance,
            market_regime_stability=regime_stability,
            parameter_sensitivity=param_sensitivity,
            drawdown_recovery=dd_recovery,
            tail_risk_management=tail_risk,
            live_simulation_score=live_sim_score,
            overall_robustness=overall_robustness
        )

    def _test_walk_forward_consistency(
        self,
        strategy_config: Dict[str, Any],
        data: pd.DataFrame
    ) -> float:
        """Test strategy consistency across multiple time periods."""
        try:
            # Split data into multiple periods
            period_length = len(data) // self.validation_periods
            if period_length < 100:
                return 0.5  # Not enough data

            period_returns = []

            for i in range(self.validation_periods):
                start_idx = i * period_length
                end_idx = min((i + 1) * period_length, len(data))
                period_data = data.iloc[start_idx:end_idx]

                if len(period_data) < 50:
                    continue

                # Create strategy and test
                strategy = strategy_config['class'](
                    strategy_config['symbol'],
                    strategy_config['timeframe'],
                    **strategy_config['parameters']
                )

                result = self.backtester.run_backtest(strategy, period_data)
                period_returns.append(result.total_return)

            if len(period_returns) < 3:
                return 0.3

            # Calculate consistency score
            mean_return = np.mean(period_returns)
            std_return = np.std(period_returns)
            positive_periods = sum(1 for r in period_returns if r > 0)

            consistency_score = (
                (positive_periods / len(period_returns)) * 0.6 +  # Positive ratio
                (1 / (1 + std_return)) * 0.4  # Stability
            )

            return min(1.0, max(0.0, consistency_score))

        except Exception:
            return 0.2

    def _test_out_of_sample_performance(
        self,
        strategy_config: Dict[str, Any],
        data: pd.DataFrame
    ) -> float:
        """Test performance on out-of-sample data."""
        try:
            # Split data: 70% in-sample, 30% out-of-sample
            split_point = int(len(data) * (1 - self.out_of_sample_ratio))
            in_sample_data = data.iloc[:split_point]
            out_sample_data = data.iloc[split_point:]

            if len(out_sample_data) < 100:
                return 0.4

            # Test on in-sample data
            strategy_in = strategy_config['class'](
                strategy_config['symbol'],
                strategy_config['timeframe'],
                **strategy_config['parameters']
            )
            in_sample_result = self.backtester.run_backtest(strategy_in, in_sample_data)

            # Test on out-of-sample data
            strategy_out = strategy_config['class'](
                strategy_config['symbol'],
                strategy_config['timeframe'],
                **strategy_config['parameters']
            )
            out_sample_result = self.backtester.run_backtest(strategy_out, out_sample_data)

            # Compare performance
            in_sample_sharpe = max(in_sample_result.sharpe_ratio, -2)
            out_sample_sharpe = max(out_sample_result.sharpe_ratio, -2)

            # Performance degradation score
            if in_sample_sharpe <= 0:
                return 0.3

            performance_ratio = out_sample_sharpe / in_sample_sharpe
            degradation_score = min(1.0, max(0.0, performance_ratio))

            return degradation_score

        except Exception:
            return 0.3

    def _test_market_regime_stability(
        self,
        strategy_config: Dict[str, Any],
        data: pd.DataFrame
    ) -> float:
        """Test strategy performance across different market regimes."""
        try:
            # Identify market regimes based on volatility and trend
            returns = data['close'].pct_change().dropna()
            rolling_vol = returns.rolling(30).std()
            rolling_trend = data['close'].rolling(30).apply(lambda x: (x[-1] - x[0]) / x[0])

            # Define regimes
            high_vol_mask = rolling_vol > rolling_vol.quantile(0.7)
            low_vol_mask = rolling_vol < rolling_vol.quantile(0.3)
            bull_mask = rolling_trend > rolling_trend.quantile(0.7)
            bear_mask = rolling_trend < rolling_trend.quantile(0.3)

            regimes = {
                'high_vol': data[high_vol_mask],
                'low_vol': data[low_vol_mask],
                'bull_market': data[bull_mask],
                'bear_market': data[bear_mask]
            }

            regime_scores = []

            for regime_name, regime_data in regimes.items():
                if len(regime_data) < 50:
                    continue

                strategy = strategy_config['class'](
                    strategy_config['symbol'],
                    strategy_config['timeframe'],
                    **strategy_config['parameters']
                )

                result = self.backtester.run_backtest(strategy, regime_data)

                # Score based on Sharpe ratio and max drawdown
                sharpe_score = min(1.0, max(0.0, (result.sharpe_ratio + 1) / 3))
                dd_score = min(1.0, max(0.0, 1 - result.max_drawdown))
                regime_score = (sharpe_score + dd_score) / 2

                regime_scores.append(regime_score)

            if not regime_scores:
                return 0.4

            # Return average performance across regimes
            return np.mean(regime_scores)

        except Exception:
            return 0.4

    def _test_parameter_sensitivity(
        self,
        strategy_config: Dict[str, Any],
        data: pd.DataFrame
    ) -> float:
        """Test sensitivity to parameter changes."""
        try:
            base_params = strategy_config['parameters'].copy()
            sensitivity_scores = []

            # Test each parameter
            for param_name, base_value in base_params.items():
                if isinstance(base_value, (int, float)):
                    # Test parameter variations
                    variations = [0.8, 0.9, 1.1, 1.2]  # ±20% and ±10%
                    param_scores = []

                    for variation in variations:
                        test_params = base_params.copy()

                        if isinstance(base_value, int):
                            test_params[param_name] = max(1, int(base_value * variation))
                        else:
                            test_params[param_name] = base_value * variation

                        try:
                            strategy = strategy_config['class'](
                                strategy_config['symbol'],
                                strategy_config['timeframe'],
                                **test_params
                            )

                            result = self.backtester.run_backtest(strategy, data)
                            param_scores.append(result.sharpe_ratio)

                        except Exception:
                            param_scores.append(-2)

                    # Calculate parameter stability
                    if param_scores:
                        param_std = np.std(param_scores)
                        param_mean = np.mean(param_scores)

                        # Lower std relative to mean = more stable
                        if param_mean > 0:
                            stability = 1 / (1 + param_std / abs(param_mean))
                        else:
                            stability = 1 / (1 + param_std)

                        sensitivity_scores.append(stability)

            if not sensitivity_scores:
                return 0.5

            return np.mean(sensitivity_scores)

        except Exception:
            return 0.5

    def _test_drawdown_recovery(
        self,
        strategy_config: Dict[str, Any],
        data: pd.DataFrame
    ) -> float:
        """Test strategy's ability to recover from drawdowns."""
        try:
            strategy = strategy_config['class'](
                strategy_config['symbol'],
                strategy_config['timeframe'],
                **strategy_config['parameters']
            )

            result = self.backtester.run_backtest(strategy, data)

            if not hasattr(result, 'equity_curve') or len(result.equity_curve) < 100:
                return 0.4

            equity_curve = np.array(result.equity_curve)

            # Find drawdown periods
            peak = equity_curve[0]
            drawdowns = []
            recovery_times = []

            in_drawdown = False
            drawdown_start = 0

            for i, value in enumerate(equity_curve):
                if value > peak:
                    if in_drawdown:
                        # Recovery completed
                        recovery_time = i - drawdown_start
                        recovery_times.append(recovery_time)
                        in_drawdown = False
                    peak = value
                elif value < peak * 0.95:  # 5% drawdown threshold
                    if not in_drawdown:
                        in_drawdown = True
                        drawdown_start = i
                        drawdown_depth = (peak - value) / peak
                        drawdowns.append(drawdown_depth)

            if not drawdowns:
                return 0.8  # No significant drawdowns

            # Score based on recovery efficiency
            avg_drawdown = np.mean(drawdowns)
            avg_recovery_time = np.mean(recovery_times) if recovery_times else len(equity_curve)

            # Normalize recovery time (shorter is better)
            recovery_score = 1 / (1 + avg_recovery_time / 100)

            # Combine with drawdown magnitude
            drawdown_score = 1 - min(0.8, avg_drawdown)

            return (recovery_score + drawdown_score) / 2

        except Exception:
            return 0.4

    def _test_tail_risk_management(
        self,
        strategy_config: Dict[str, Any],
        data: pd.DataFrame
    ) -> float:
        """Test strategy's performance during extreme market events."""
        try:
            # Calculate daily returns
            returns = data['close'].pct_change().dropna()

            if len(returns) < 100:
                return 0.4

            # Identify extreme market days (top/bottom 5%)
            extreme_threshold = 0.05
            extreme_down_days = returns <= returns.quantile(extreme_threshold)
            extreme_up_days = returns >= returns.quantile(1 - extreme_threshold)

            # Get data for extreme periods
            extreme_periods = []

            for i, is_extreme in enumerate(extreme_down_days | extreme_up_days):
                if is_extreme and i >= 30:  # Need some history
                    start_idx = max(0, i - 30)
                    end_idx = min(len(data), i + 10)
                    extreme_periods.append(data.iloc[start_idx:end_idx])

            if not extreme_periods:
                return 0.5

            # Test strategy during extreme periods
            extreme_scores = []

            for period_data in extreme_periods[:10]:  # Test up to 10 periods
                if len(period_data) < 20:
                    continue

                try:
                    strategy = strategy_config['class'](
                        strategy_config['symbol'],
                        strategy_config['timeframe'],
                        **strategy_config['parameters']
                    )

                    result = self.backtester.run_backtest(strategy, period_data)

                    # Score based on drawdown control during extreme events
                    dd_score = 1 - min(0.9, result.max_drawdown)
                    return_score = min(1.0, max(0.0, (result.total_return + 0.1) / 0.2))

                    extreme_score = (dd_score * 0.7 + return_score * 0.3)
                    extreme_scores.append(extreme_score)

                except Exception:
                    extreme_scores.append(0.2)

            if not extreme_scores:
                return 0.4

            return np.mean(extreme_scores)

        except Exception:
            return 0.4

    def _select_elite(self, population: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Select elite strategies based on fitness score."""
        # Sort by fitness score (descending)
        sorted_population = sorted(
            population,
            key=lambda x: x['fitness_score'],
            reverse=True
        )

        elite_count = max(1, int(len(population) * self.elite_ratio))
        return sorted_population[:elite_count]

    def _create_generation_record(
        self,
        generation: int,
        population: List[Dict[str, Any]],
        elite_strategies: List[Dict[str, Any]]
    ) -> EvolutionGeneration:
        """Create a record of the current generation."""

        # Calculate population statistics
        fitness_scores = [s['fitness_score'] for s in population]
        robustness_scores = [s.get('robustness_score', 0) for s in population]

        population_stats = {
            'mean_fitness': np.mean(fitness_scores),
            'std_fitness': np.std(fitness_scores),
            'max_fitness': np.max(fitness_scores),
            'min_fitness': np.min(fitness_scores),
            'mean_robustness': np.mean(robustness_scores),
            'population_size': len(population)
        }

        # Calculate improvements from previous generation
        improvements = {}
        if self.generations:
            prev_gen = self.generations[-1]
            improvements = {
                'fitness_improvement': population_stats['max_fitness'] - prev_gen.population_stats['max_fitness'],
                'robustness_improvement': population_stats['mean_robustness'] - prev_gen.population_stats['mean_robustness']
            }

        return EvolutionGeneration(
            generation=generation,
            timestamp=datetime.now(),
            strategies=[s.copy() for s in population],
            best_strategy=elite_strategies[0].copy() if elite_strategies else {},
            population_stats=population_stats,
            improvements=improvements,
            validation_results={}
        )

    def _check_convergence(self) -> bool:
        """Check if evolution has converged."""
        if len(self.generations) < 5:
            return False

        # Check if fitness has plateaued
        recent_fitness = [gen.population_stats['max_fitness'] for gen in self.generations[-5:]]
        fitness_improvement = max(recent_fitness) - min(recent_fitness)

        # Converged if improvement is less than 1%
        return fitness_improvement < 0.01

    def _generate_next_population(
        self,
        elite_strategies: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Generate next population through mutation and crossover."""
        next_population = []

        # Keep elite strategies
        for elite in elite_strategies:
            next_population.append(elite.copy())

        # Generate offspring through mutation and crossover
        while len(next_population) < self.population_size:
            if np.random.random() < 0.7:  # 70% mutation, 30% crossover
                # Mutation
                parent = np.random.choice(elite_strategies)
                offspring = self._mutate_strategy(parent)
            else:
                # Crossover
                if len(elite_strategies) >= 2:
                    parent1, parent2 = np.random.choice(elite_strategies, 2, replace=False)
                    offspring = self._crossover_strategies(parent1, parent2)
                else:
                    parent = np.random.choice(elite_strategies)
                    offspring = self._mutate_strategy(parent)

            if offspring:
                next_population.append(offspring)

        return next_population[:self.population_size]

    def _mutate_strategy(self, strategy: Dict[str, Any]) -> Dict[str, Any]:
        """Mutate a strategy's parameters."""
        mutated = strategy.copy()
        mutated['parameters'] = strategy['parameters'].copy()
        mutated['generation'] = strategy.get('generation', 0) + 1

        # Mutate each parameter with some probability
        for param_name, param_value in mutated['parameters'].items():
            if np.random.random() < self.mutation_rate:
                if isinstance(param_value, int):
                    # Integer parameter
                    mutation_range = max(1, int(param_value * 0.2))
                    mutation = np.random.randint(-mutation_range, mutation_range + 1)
                    mutated['parameters'][param_name] = max(1, param_value + mutation)
                elif isinstance(param_value, float):
                    # Float parameter
                    mutation_factor = np.random.normal(1.0, 0.1)
                    mutated['parameters'][param_name] = param_value * mutation_factor

        # Update name
        mutated['name'] = f"{strategy['name']}_mut_{mutated['generation']}"

        return mutated

    def _crossover_strategies(
        self,
        parent1: Dict[str, Any],
        parent2: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create offspring through parameter crossover."""
        offspring = parent1.copy()
        offspring['parameters'] = parent1['parameters'].copy()
        offspring['generation'] = max(
            parent1.get('generation', 0),
            parent2.get('generation', 0)
        ) + 1

        # Crossover parameters
        for param_name in offspring['parameters']:
            if param_name in parent2['parameters']:
                if np.random.random() < 0.5:
                    offspring['parameters'][param_name] = parent2['parameters'][param_name]

        # Update name
        offspring['name'] = f"{parent1['name']}_x_{parent2['name']}_gen_{offspring['generation']}"

        return offspring

    def _generate_final_results(self, total_time: float) -> Dict[str, Any]:
        """Generate comprehensive final results."""

        if not self.best_ever_strategy:
            return {'error': 'No successful strategies found'}

        # Compile evolution statistics
        evolution_stats = {
            'total_generations': len(self.generations),
            'total_time_minutes': total_time / 60,
            'convergence_generation': self._find_convergence_generation(),
            'improvement_rate': self._calculate_improvement_rate(),
            'final_population_diversity': self._calculate_population_diversity()
        }

        # Best strategy detailed analysis
        best_strategy_analysis = self._analyze_best_strategy()

        # Robustness validation summary
        robustness_summary = self._summarize_robustness_metrics()

        # Performance comparison
        performance_comparison = self._compare_strategy_performance()

        final_results = {
            'best_strategy': self.best_ever_strategy,
            'evolution_statistics': evolution_stats,
            'best_strategy_analysis': best_strategy_analysis,
            'robustness_summary': robustness_summary,
            'performance_comparison': performance_comparison,
            'generation_history': [asdict(gen) for gen in self.generations],
            'validation_passed': self._validate_final_strategy(),
            'production_ready': self._assess_production_readiness()
        }

        # Save results
        self._save_evolution_results(final_results)

        return final_results

    def _find_convergence_generation(self) -> int:
        """Find the generation where convergence occurred."""
        if len(self.generations) < 5:
            return len(self.generations)

        for i in range(4, len(self.generations)):
            recent_fitness = [
                self.generations[j].population_stats['max_fitness']
                for j in range(i-4, i+1)
            ]
            if max(recent_fitness) - min(recent_fitness) < 0.01:
                return i

        return len(self.generations)

    def _calculate_improvement_rate(self) -> float:
        """Calculate the rate of improvement over generations."""
        if len(self.generations) < 2:
            return 0.0

        first_fitness = self.generations[0].population_stats['max_fitness']
        last_fitness = self.generations[-1].population_stats['max_fitness']

        return (last_fitness - first_fitness) / len(self.generations)

    def _calculate_population_diversity(self) -> float:
        """Calculate diversity of the final population."""
        if not self.generations:
            return 0.0

        final_gen = self.generations[-1]
        fitness_scores = [s['fitness_score'] for s in final_gen.strategies]

        return np.std(fitness_scores) / (np.mean(fitness_scores) + 1e-8)

    def _analyze_best_strategy(self) -> Dict[str, Any]:
        """Perform detailed analysis of the best strategy."""
        if not self.best_ever_strategy:
            return {}

        strategy = self.best_ever_strategy

        return {
            'strategy_name': strategy['name'],
            'strategy_class': strategy['class'].__name__,
            'symbol': strategy['symbol'],
            'timeframe': strategy['timeframe'],
            'optimized_parameters': strategy['parameters'],
            'performance_metrics': {
                'fitness_score': strategy['fitness_score'],
                'total_return': strategy['total_return'],
                'sharpe_ratio': strategy['sharpe_ratio'],
                'max_drawdown': strategy['max_drawdown'],
                'win_rate': strategy['win_rate'],
                'total_trades': strategy['total_trades']
            },
            'robustness_metrics': strategy.get('robustness_metrics', {}),
            'generation_discovered': strategy.get('generation', 0),
            'evolution_lineage': self._trace_strategy_lineage(strategy)
        }

    def _trace_strategy_lineage(self, strategy: Dict[str, Any]) -> List[str]:
        """Trace the evolutionary lineage of a strategy."""
        lineage = []
        current_name = strategy['name']

        # Simple lineage tracking based on naming convention
        if '_mut_' in current_name:
            lineage.append('mutation')
        if '_x_' in current_name:
            lineage.append('crossover')

        return lineage

    def _summarize_robustness_metrics(self) -> Dict[str, Any]:
        """Summarize robustness metrics across all strategies."""
        if not self.best_ever_strategy or 'robustness_metrics' not in self.best_ever_strategy:
            return {}

        robustness = self.best_ever_strategy['robustness_metrics']

        return {
            'overall_robustness_score': robustness.overall_robustness,
            'walk_forward_consistency': robustness.walk_forward_consistency,
            'out_of_sample_performance': robustness.out_of_sample_performance,
            'market_regime_stability': robustness.market_regime_stability,
            'parameter_sensitivity': robustness.parameter_sensitivity,
            'drawdown_recovery': robustness.drawdown_recovery,
            'tail_risk_management': robustness.tail_risk_management,
            'live_simulation_score': robustness.live_simulation_score,
            'robustness_grade': self._grade_robustness(robustness.overall_robustness)
        }

    def _grade_robustness(self, score: float) -> str:
        """Grade robustness score."""
        if score >= 0.8:
            return 'A+ (Excellent)'
        elif score >= 0.7:
            return 'A (Very Good)'
        elif score >= 0.6:
            return 'B (Good)'
        elif score >= 0.5:
            return 'C (Fair)'
        else:
            return 'D (Poor)'

    def _compare_strategy_performance(self) -> Dict[str, Any]:
        """Compare performance across different strategy types."""
        if not self.generations:
            return {}

        # Get all strategies from final generation
        final_strategies = self.generations[-1].strategies

        # Group by strategy class
        strategy_groups = {}
        for strategy in final_strategies:
            class_name = strategy['class'].__name__
            if class_name not in strategy_groups:
                strategy_groups[class_name] = []
            strategy_groups[class_name].append(strategy)

        # Calculate statistics for each group
        comparison = {}
        for class_name, strategies in strategy_groups.items():
            fitness_scores = [s['fitness_score'] for s in strategies]
            robustness_scores = [s.get('robustness_score', 0) for s in strategies]

            comparison[class_name] = {
                'count': len(strategies),
                'avg_fitness': np.mean(fitness_scores),
                'max_fitness': np.max(fitness_scores),
                'avg_robustness': np.mean(robustness_scores),
                'best_strategy': max(strategies, key=lambda x: x['fitness_score'])['name']
            }

        return comparison

    def _validate_final_strategy(self) -> bool:
        """Validate that the final strategy meets production criteria."""
        if not self.best_ever_strategy:
            return False

        strategy = self.best_ever_strategy

        # Validation criteria
        criteria = {
            'min_fitness_score': 0.5,
            'min_robustness_score': 0.6,
            'min_total_trades': 30,
            'max_drawdown_threshold': 0.3,
            'min_sharpe_ratio': 0.5
        }

        validation_results = {
            'fitness_score': strategy['fitness_score'] >= criteria['min_fitness_score'],
            'robustness_score': strategy.get('robustness_score', 0) >= criteria['min_robustness_score'],
            'total_trades': strategy['total_trades'] >= criteria['min_total_trades'],
            'max_drawdown': strategy['max_drawdown'] <= criteria['max_drawdown_threshold'],
            'sharpe_ratio': strategy['sharpe_ratio'] >= criteria['min_sharpe_ratio']
        }

        return all(validation_results.values())

    def _assess_production_readiness(self) -> Dict[str, Any]:
        """Assess if the strategy is ready for production deployment."""
        if not self.best_ever_strategy:
            return {'ready': False, 'reasons': ['No strategy found']}

        strategy = self.best_ever_strategy
        robustness = strategy.get('robustness_metrics')

        readiness_checks = {
            'sufficient_performance': strategy['fitness_score'] >= 0.7,
            'robust_validation': strategy.get('robustness_score', 0) >= 0.7,
            'adequate_trading_frequency': strategy['total_trades'] >= 50,
            'controlled_risk': strategy['max_drawdown'] <= 0.25,
            'consistent_returns': robustness and robustness.walk_forward_consistency >= 0.6,
            'regime_stability': robustness and robustness.market_regime_stability >= 0.6,
            'parameter_stability': robustness and robustness.parameter_sensitivity >= 0.6
        }

        passed_checks = sum(readiness_checks.values())
        total_checks = len(readiness_checks)

        readiness_score = passed_checks / total_checks

        failed_checks = [check for check, passed in readiness_checks.items() if not passed]

        return {
            'ready': readiness_score >= 0.8,
            'readiness_score': readiness_score,
            'passed_checks': passed_checks,
            'total_checks': total_checks,
            'failed_checks': failed_checks,
            'recommendation': self._get_production_recommendation(readiness_score)
        }

    def _get_production_recommendation(self, readiness_score: float) -> str:
        """Get production deployment recommendation."""
        if readiness_score >= 0.9:
            return "READY FOR PRODUCTION - Strategy shows excellent performance and robustness"
        elif readiness_score >= 0.8:
            return "READY FOR PRODUCTION - Strategy meets all key criteria"
        elif readiness_score >= 0.7:
            return "CAUTION - Strategy shows promise but needs improvement in some areas"
        elif readiness_score >= 0.6:
            return "NOT READY - Strategy needs significant improvement before production"
        else:
            return "NOT READY - Strategy fails multiple critical criteria"

    def _save_evolution_results(self, results: Dict[str, Any]):
        """Save evolution results to database and files."""
        try:
            # Save to performance tracker
            if self.best_ever_strategy and 'backtest_result' in self.best_ever_strategy:
                self.performance_tracker.add_result(
                    strategy_name=self.best_ever_strategy['name'],
                    result=self.best_ever_strategy['backtest_result'],
                    parameters=self.best_ever_strategy['parameters'],
                    robustness_score=self.best_ever_strategy.get('robustness_score', 0),
                    notes=f"Evolution result - Generation {self.best_ever_strategy.get('generation', 0)}"
                )

            # Save detailed results to JSON
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"evolution_results_{timestamp}.json"

            # Convert non-serializable objects
            serializable_results = self._make_serializable(results)

            with open(filename, 'w') as f:
                json.dump(serializable_results, f, indent=2, default=str)

            logger.info(f"Evolution results saved to {filename}")

        except Exception as e:
            logger.error(f"Failed to save evolution results: {e}")

    def _make_serializable(self, obj):
        """Make object JSON serializable."""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif hasattr(obj, '__dict__'):
            return self._make_serializable(obj.__dict__)
        elif isinstance(obj, (datetime, pd.Timestamp)):
            return obj.isoformat()
        elif isinstance(obj, type):
            return obj.__name__
        else:
            return obj