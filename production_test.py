"""
Production readiness test for the cryptocurrency trading framework.
Tests all core functionality to ensure everything is working correctly.
"""
import sys
import os
import time
import traceback
import numpy as np
import pandas as pd

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test all critical imports."""
    print("🔍 Testing imports...")
    
    try:
        # Core imports
        from config.settings import settings
        from core.data_manager import DataManager
        from core.backtester import VectorizedBacktester
        from core.optimizer import StrategyOptimizer
        
        # Strategy imports
        from strategies.base_strategy import BaseStrategy, Signal, Position
        from strategies.technical_strategies import (
            MovingAverageCrossover, RSIMeanReversion, 
            BollingerBandsMomentum, MACDStrategy
        )
        
        # Utils imports
        from utils.metrics import AdvancedMetrics, calculate_sharpe_ratio
        from utils.performance_tracker import PerformanceTracker
        
        print("✅ All imports successful")
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        traceback.print_exc()
        return False


def test_strategy_creation():
    """Test strategy creation and basic functionality."""
    print("\n🎯 Testing strategy creation...")
    
    try:
        from strategies.technical_strategies import MovingAverageCrossover
        
        # Create strategy
        strategy = MovingAverageCrossover(
            symbol="BTCUSDT",
            timeframe="1h",
            fast_period=10,
            slow_period=30
        )
        
        print(f"✅ Strategy created: {strategy.name}")
        print(f"   Parameters: {strategy.parameters}")
        
        return True
        
    except Exception as e:
        print(f"❌ Strategy creation error: {e}")
        traceback.print_exc()
        return False


def test_data_generation():
    """Test sample data generation."""
    print("\n📊 Testing data generation...")
    
    try:
        # Generate realistic sample data
        np.random.seed(42)
        dates = pd.date_range(start='2023-01-01', periods=1000, freq='1H')
        
        returns = np.random.normal(0.0001, 0.02, len(dates))
        prices = 30000 * np.exp(np.cumsum(returns))
        
        data = pd.DataFrame(index=dates)
        data['close'] = prices
        data['open'] = data['close'].shift(1).fillna(data['close'].iloc[0])
        data['high'] = np.maximum(data['open'], data['close']) * (1 + np.random.uniform(0, 0.01, len(data)))
        data['low'] = np.minimum(data['open'], data['close']) * (1 - np.random.uniform(0, 0.01, len(data)))
        data['volume'] = np.random.uniform(100, 1000, len(data))
        
        print(f"✅ Generated {len(data)} data points")
        print(f"   Date range: {data.index[0]} to {data.index[-1]}")
        print(f"   Price range: ${data['close'].min():.2f} - ${data['close'].max():.2f}")
        
        return data
        
    except Exception as e:
        print(f"❌ Data generation error: {e}")
        traceback.print_exc()
        return None


def test_signal_generation(data):
    """Test signal generation."""
    print("\n📡 Testing signal generation...")
    
    try:
        from strategies.technical_strategies import MovingAverageCrossover
        
        strategy = MovingAverageCrossover("BTCUSDT", "1h", fast_period=5, slow_period=15)
        
        # Initialize strategy
        strategy.initialize(data.head(100))
        
        # Generate signal
        start_time = time.perf_counter()
        signal = strategy.process_data(data.head(200))
        execution_time = time.perf_counter() - start_time
        
        print(f"✅ Signal generated in {execution_time*1000:.2f}ms")
        print(f"   Action: {signal.action}")
        print(f"   Price: ${signal.price:.2f}")
        print(f"   Confidence: {signal.confidence:.2f}")
        print(f"   Metadata: {signal.metadata}")
        
        return True
        
    except Exception as e:
        print(f"❌ Signal generation error: {e}")
        traceback.print_exc()
        return False


def test_backtesting(data):
    """Test backtesting functionality."""
    print("\n🔬 Testing backtesting...")
    
    try:
        from strategies.technical_strategies import RSIMeanReversion
        from core.backtester import VectorizedBacktester
        
        # Create strategy and backtester
        strategy = RSIMeanReversion("BTCUSDT", "1h", rsi_period=14)
        backtester = VectorizedBacktester(initial_capital=10000)
        
        # Run backtest
        start_time = time.perf_counter()
        result = backtester.run_backtest(strategy, data)
        execution_time = time.perf_counter() - start_time
        
        print(f"✅ Backtest completed in {execution_time:.4f}s")
        print(f"   Initial Capital: ${result.initial_capital:,.2f}")
        print(f"   Final Capital: ${result.final_capital:,.2f}")
        print(f"   Total Return: {result.total_return:.2%}")
        print(f"   Sharpe Ratio: {result.sharpe_ratio:.4f}")
        print(f"   Max Drawdown: {result.max_drawdown:.2%}")
        print(f"   Win Rate: {result.win_rate:.2%}")
        print(f"   Total Trades: {result.total_trades}")
        
        return result
        
    except Exception as e:
        print(f"❌ Backtesting error: {e}")
        traceback.print_exc()
        return None


def test_metrics_calculation():
    """Test advanced metrics calculation."""
    print("\n📈 Testing metrics calculation...")
    
    try:
        from utils.metrics import AdvancedMetrics, calculate_sharpe_ratio
        
        # Generate sample returns
        np.random.seed(42)
        returns = np.random.normal(0.001, 0.02, 252)
        equity_curve = 10000 * np.cumprod(1 + returns)
        
        # Calculate metrics
        start_time = time.perf_counter()
        metrics = AdvancedMetrics.calculate_all_metrics(returns, equity_curve)
        execution_time = time.perf_counter() - start_time
        
        print(f"✅ Metrics calculated in {execution_time*1000:.2f}ms")
        print(f"   Sharpe Ratio: {metrics['sharpe_ratio']:.4f}")
        print(f"   Sortino Ratio: {metrics['sortino_ratio']:.4f}")
        print(f"   Max Drawdown: {metrics['max_drawdown']:.2%}")
        print(f"   Total Return: {metrics['total_return']:.2%}")
        
        return True
        
    except Exception as e:
        print(f"❌ Metrics calculation error: {e}")
        traceback.print_exc()
        return False


def test_performance_tracking(backtest_result):
    """Test performance tracking."""
    print("\n📊 Testing performance tracking...")
    
    try:
        from utils.performance_tracker import PerformanceTracker
        
        # Create tracker
        tracker = PerformanceTracker(db_path="test_performance.db")
        
        # Add result
        if backtest_result:
            tracker.add_result(
                strategy_name="TestStrategy",
                result=backtest_result,
                parameters={'test': True},
                robustness_score=0.8
            )
            
            print("✅ Performance tracking successful")
            print("   Result added to database")
            
            # Clean up test database
            try:
                os.remove("test_performance.db")
                print("   Test database cleaned up")
            except:
                pass
            
            return True
        else:
            print("⚠️ No backtest result to track")
            return False
        
    except Exception as e:
        print(f"❌ Performance tracking error: {e}")
        traceback.print_exc()
        return False


def test_api_connection():
    """Test Binance API connection if credentials are available."""
    print("\n🔗 Testing API connection...")
    
    try:
        from config.settings import settings
        from core.data_manager import DataManager
        
        # Check if credentials are configured
        if (settings.binance_api_key == "your_binance_api_key_here" or 
            not settings.binance_api_key or not settings.binance_api_secret):
            print("⚠️ API credentials not configured - skipping API test")
            return True
        
        # Test connection
        data_manager = DataManager()
        price = data_manager.get_latest_price("BTCUSDT")
        
        if price > 0:
            print(f"✅ API connection successful")
            print(f"   Current BTC price: ${price:,.2f}")
            return True
        else:
            print("❌ Failed to get price from API")
            return False
            
    except Exception as e:
        print(f"❌ API connection error: {e}")
        return False


def test_all_strategies(data):
    """Test all implemented strategies."""
    print("\n🧪 Testing all strategies...")
    
    strategies = [
        ("MovingAverageCrossover", {"fast_period": 5, "slow_period": 15}),
        ("RSIMeanReversion", {"rsi_period": 14}),
        ("BollingerBandsMomentum", {"bb_period": 20}),
        ("MACDStrategy", {"fast_period": 12, "slow_period": 26})
    ]
    
    successful = 0
    
    for strategy_name, params in strategies:
        try:
            from strategies.technical_strategies import (
                MovingAverageCrossover, RSIMeanReversion,
                BollingerBandsMomentum, MACDStrategy
            )
            
            strategy_class = globals()[strategy_name]
            strategy = strategy_class("BTCUSDT", "1h", **params)
            
            # Test signal generation
            strategy.initialize(data.head(100))
            signal = strategy.process_data(data.head(200))
            
            print(f"   ✅ {strategy_name}: {signal.action} signal")
            successful += 1
            
        except Exception as e:
            print(f"   ❌ {strategy_name}: {e}")
    
    print(f"✅ {successful}/{len(strategies)} strategies working")
    return successful == len(strategies)


def run_production_test():
    """Run comprehensive production readiness test."""
    print("🚀 CRYPTOCURRENCY TRADING FRAMEWORK - PRODUCTION TEST")
    print("=" * 70)
    print("Testing all components for production readiness...")
    
    start_time = time.time()
    
    tests = [
        ("Imports", test_imports),
        ("Strategy Creation", test_strategy_creation),
        ("Data Generation", test_data_generation),
        ("API Connection", test_api_connection),
    ]
    
    # Run initial tests
    results = {}
    data = None
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        try:
            if test_name == "Data Generation":
                data = test_func()
                results[test_name] = data is not None
            else:
                results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Run tests that require data
    if data is not None:
        data_dependent_tests = [
            ("Signal Generation", lambda: test_signal_generation(data)),
            ("Backtesting", lambda: test_backtesting(data)),
            ("All Strategies", lambda: test_all_strategies(data)),
        ]
        
        backtest_result = None
        
        for test_name, test_func in data_dependent_tests:
            print(f"\n{'='*50}")
            try:
                if test_name == "Backtesting":
                    backtest_result = test_backtesting(data)
                    results[test_name] = backtest_result is not None
                else:
                    results[test_name] = test_func()
            except Exception as e:
                print(f"❌ {test_name} failed with exception: {e}")
                results[test_name] = False
        
        # Test performance tracking
        print(f"\n{'='*50}")
        try:
            results["Performance Tracking"] = test_performance_tracking(backtest_result)
        except Exception as e:
            print(f"❌ Performance Tracking failed with exception: {e}")
            results["Performance Tracking"] = False
    
    # Test metrics calculation
    print(f"\n{'='*50}")
    try:
        results["Metrics Calculation"] = test_metrics_calculation()
    except Exception as e:
        print(f"❌ Metrics Calculation failed with exception: {e}")
        results["Metrics Calculation"] = False
    
    # Final results
    total_time = time.time() - start_time
    
    print(f"\n{'='*70}")
    print("🏁 PRODUCTION TEST RESULTS")
    print(f"{'='*70}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:25} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    print(f"Execution time: {total_time:.2f} seconds")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED - FRAMEWORK IS PRODUCTION READY!")
        print("\nFramework is ready for:")
        print("  ✅ Real-time trading with Binance data")
        print("  ✅ Strategy optimization and backtesting")
        print("  ✅ Automated execution without user input")
        print("  ✅ Performance tracking and analysis")
        
        print("\nNext steps:")
        print("  1. Configure Binance API credentials in .env")
        print("  2. Run: python start.py")
        print("  3. Or run: python auto_run.py")
        
    else:
        print(f"\n⚠️ {total-passed} tests failed - review errors above")
        print("Framework may still work but with limited functionality")
    
    return passed == total


if __name__ == "__main__":
    success = run_production_test()
    sys.exit(0 if success else 1)
