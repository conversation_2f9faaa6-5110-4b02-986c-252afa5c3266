"""
Performance tracking and comparison utilities.
Tracks strategy performance over time and provides detailed analytics.
"""
import json
import sqlite3
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from datetime import datetime
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from dataclasses import asdict
from loguru import logger

from core.backtester import BacktestResult
from config.settings import settings


class PerformanceTracker:
    """
    Comprehensive performance tracking and analytics system.
    Stores and compares strategy performance over time.
    """
    
    def __init__(self, db_path: str = "performance_tracking.db"):
        self.db_path = db_path
        self._init_database()
    
    def _init_database(self):
        """Initialize performance tracking database."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Strategy performance table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS strategy_performance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                strategy_name TEXT NOT NULL,
                symbol TEXT NOT NULL,
                timeframe TEXT NOT NULL,
                test_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                parameters TEXT,
                total_return REAL,
                annual_return REAL,
                sharpe_ratio REAL,
                sortino_ratio REAL,
                calmar_ratio REAL,
                max_drawdown REAL,
                win_rate REAL,
                profit_factor REAL,
                total_trades INTEGER,
                avg_trade_duration REAL,
                execution_time REAL,
                robustness_score REAL,
                notes TEXT
            )
        """)
        
        # Trade details table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_details (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                performance_id INTEGER,
                symbol TEXT,
                side TEXT,
                entry_price REAL,
                exit_price REAL,
                quantity REAL,
                entry_time TIMESTAMP,
                exit_time TIMESTAMP,
                pnl REAL,
                pnl_percentage REAL,
                duration_hours REAL,
                FOREIGN KEY (performance_id) REFERENCES strategy_performance (id)
            )
        """)
        
        # Performance comparison table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS performance_comparison (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                comparison_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                strategy_name TEXT,
                baseline_performance_id INTEGER,
                optimized_performance_id INTEGER,
                improvement_percentage REAL,
                notes TEXT,
                FOREIGN KEY (baseline_performance_id) REFERENCES strategy_performance (id),
                FOREIGN KEY (optimized_performance_id) REFERENCES strategy_performance (id)
            )
        """)
        
        conn.commit()
        conn.close()
        
        logger.info("Performance tracking database initialized")
    
    def add_result(
        self,
        strategy_name: str,
        result: BacktestResult,
        parameters: Dict[str, Any],
        robustness_score: float = 0.0,
        notes: str = ""
    ) -> int:
        """
        Add backtest result to performance tracking.
        
        Args:
            strategy_name: Name of the strategy
            result: BacktestResult object
            parameters: Strategy parameters used
            robustness_score: Robustness score from optimization
            notes: Additional notes
            
        Returns:
            Performance record ID
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Insert strategy performance
        cursor.execute("""
            INSERT INTO strategy_performance (
                strategy_name, symbol, timeframe, parameters,
                total_return, annual_return, sharpe_ratio, sortino_ratio,
                calmar_ratio, max_drawdown, win_rate, profit_factor,
                total_trades, avg_trade_duration, execution_time,
                robustness_score, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            strategy_name, result.symbol, result.timeframe,
            json.dumps(parameters), result.total_return, result.annual_return,
            result.sharpe_ratio, result.sortino_ratio, result.calmar_ratio,
            result.max_drawdown, result.win_rate, result.profit_factor,
            result.total_trades, result.avg_trade_duration, result.execution_time,
            robustness_score, notes
        ))
        
        performance_id = cursor.lastrowid
        
        # Insert trade details
        for trade in result.trades:
            duration_hours = trade['duration'].total_seconds() / 3600 if 'duration' in trade else 0
            
            cursor.execute("""
                INSERT INTO trade_details (
                    performance_id, symbol, side, entry_price, exit_price,
                    quantity, entry_time, exit_time, pnl, pnl_percentage,
                    duration_hours
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                performance_id, trade.get('symbol', result.symbol),
                trade.get('side', 'UNKNOWN'), trade.get('entry_price', 0),
                trade.get('exit_price', 0), trade.get('quantity', 0),
                trade.get('entry_time'), trade.get('exit_time'),
                trade.get('pnl', 0), trade.get('pnl_percentage', 0),
                duration_hours
            ))
        
        conn.commit()
        conn.close()
        
        logger.info(f"Added performance record {performance_id} for {strategy_name}")
        return performance_id
    
    def get_strategy_history(
        self,
        strategy_name: str,
        symbol: Optional[str] = None,
        limit: int = 50
    ) -> pd.DataFrame:
        """Get historical performance for a strategy."""
        conn = sqlite3.connect(self.db_path)
        
        query = """
            SELECT * FROM strategy_performance 
            WHERE strategy_name = ?
        """
        params = [strategy_name]
        
        if symbol:
            query += " AND symbol = ?"
            params.append(symbol)
        
        query += " ORDER BY test_date DESC LIMIT ?"
        params.append(limit)
        
        df = pd.read_sql_query(query, conn, params=params)
        conn.close()
        
        return df
    
    def compare_strategies(
        self,
        strategy_names: List[str],
        symbol: Optional[str] = None,
        metric: str = 'sharpe_ratio'
    ) -> pd.DataFrame:
        """Compare multiple strategies on a specific metric."""
        conn = sqlite3.connect(self.db_path)
        
        query = """
            SELECT strategy_name, symbol, timeframe, test_date,
                   total_return, sharpe_ratio, max_drawdown, win_rate,
                   total_trades, robustness_score
            FROM strategy_performance 
            WHERE strategy_name IN ({})
        """.format(','.join(['?' for _ in strategy_names]))
        
        params = strategy_names
        
        if symbol:
            query += " AND symbol = ?"
            params.append(symbol)
        
        query += " ORDER BY test_date DESC"
        
        df = pd.read_sql_query(query, conn, params=params)
        conn.close()
        
        return df
    
    def get_best_strategies(
        self,
        metric: str = 'sharpe_ratio',
        min_trades: int = 10,
        limit: int = 10
    ) -> pd.DataFrame:
        """Get best performing strategies based on a metric."""
        conn = sqlite3.connect(self.db_path)
        
        query = f"""
            SELECT strategy_name, symbol, timeframe, parameters,
                   total_return, sharpe_ratio, max_drawdown, win_rate,
                   total_trades, robustness_score, test_date
            FROM strategy_performance 
            WHERE total_trades >= ?
            ORDER BY {metric} DESC
            LIMIT ?
        """
        
        df = pd.read_sql_query(query, conn, params=[min_trades, limit])
        conn.close()
        
        return df
    
    def analyze_strategy_evolution(
        self,
        strategy_name: str,
        symbol: Optional[str] = None
    ) -> Dict[str, Any]:
        """Analyze how a strategy's performance has evolved over time."""
        history = self.get_strategy_history(strategy_name, symbol, limit=100)
        
        if history.empty:
            return {}
        
        # Convert test_date to datetime
        history['test_date'] = pd.to_datetime(history['test_date'])
        history = history.sort_values('test_date')
        
        # Calculate trends
        metrics = ['total_return', 'sharpe_ratio', 'max_drawdown', 'win_rate']
        trends = {}
        
        for metric in metrics:
            if len(history) > 1:
                # Linear regression to find trend
                x = np.arange(len(history))
                y = history[metric].values
                
                # Remove NaN values
                valid_idx = ~np.isnan(y)
                if np.sum(valid_idx) > 1:
                    slope, intercept = np.polyfit(x[valid_idx], y[valid_idx], 1)
                    trends[metric] = {
                        'slope': slope,
                        'direction': 'improving' if slope > 0 else 'declining',
                        'latest_value': y[-1] if not np.isnan(y[-1]) else 0,
                        'best_value': np.nanmax(y),
                        'worst_value': np.nanmin(y)
                    }
        
        # Performance stability
        stability_metrics = {}
        for metric in metrics:
            values = history[metric].dropna()
            if len(values) > 1:
                stability_metrics[metric] = {
                    'mean': values.mean(),
                    'std': values.std(),
                    'coefficient_of_variation': values.std() / abs(values.mean()) if values.mean() != 0 else np.inf
                }
        
        return {
            'total_tests': len(history),
            'date_range': {
                'first_test': history['test_date'].min(),
                'latest_test': history['test_date'].max()
            },
            'trends': trends,
            'stability': stability_metrics,
            'recent_performance': history.tail(5)[metrics].to_dict('records')
        }
    
    def generate_performance_report(
        self,
        strategy_name: Optional[str] = None,
        symbol: Optional[str] = None,
        days_back: int = 30
    ) -> str:
        """Generate comprehensive performance report."""
        conn = sqlite3.connect(self.db_path)
        
        # Base query
        query = """
            SELECT * FROM strategy_performance 
            WHERE test_date >= datetime('now', '-{} days')
        """.format(days_back)
        
        params = []
        
        if strategy_name:
            query += " AND strategy_name = ?"
            params.append(strategy_name)
        
        if symbol:
            query += " AND symbol = ?"
            params.append(symbol)
        
        query += " ORDER BY test_date DESC"
        
        df = pd.read_sql_query(query, conn, params=params)
        conn.close()
        
        if df.empty:
            return "No performance data found for the specified criteria."
        
        # Generate report
        report = []
        report.append("=" * 80)
        report.append("PERFORMANCE REPORT")
        report.append("=" * 80)
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Period: Last {days_back} days")
        
        if strategy_name:
            report.append(f"Strategy: {strategy_name}")
        if symbol:
            report.append(f"Symbol: {symbol}")
        
        report.append(f"Total Tests: {len(df)}")
        report.append("")
        
        # Summary statistics
        report.append("SUMMARY STATISTICS")
        report.append("-" * 40)
        
        metrics = ['total_return', 'sharpe_ratio', 'max_drawdown', 'win_rate']
        for metric in metrics:
            values = df[metric].dropna()
            if len(values) > 0:
                report.append(f"{metric.replace('_', ' ').title()}:")
                report.append(f"  Mean: {values.mean():.4f}")
                report.append(f"  Std:  {values.std():.4f}")
                report.append(f"  Min:  {values.min():.4f}")
                report.append(f"  Max:  {values.max():.4f}")
                report.append("")
        
        # Best performers
        report.append("TOP PERFORMERS")
        report.append("-" * 40)
        
        top_performers = df.nlargest(5, 'sharpe_ratio')
        for idx, row in top_performers.iterrows():
            report.append(f"{row['strategy_name']} ({row['symbol']}):")
            report.append(f"  Sharpe: {row['sharpe_ratio']:.4f}")
            report.append(f"  Return: {row['total_return']:.2%}")
            report.append(f"  Max DD: {row['max_drawdown']:.2%}")
            report.append(f"  Date: {row['test_date']}")
            report.append("")
        
        # Strategy comparison
        if not strategy_name:  # Only if not filtering by strategy
            report.append("STRATEGY COMPARISON")
            report.append("-" * 40)
            
            strategy_summary = df.groupby('strategy_name').agg({
                'sharpe_ratio': ['mean', 'std', 'count'],
                'total_return': 'mean',
                'max_drawdown': 'mean',
                'win_rate': 'mean'
            }).round(4)
            
            report.append(strategy_summary.to_string())
        
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def create_performance_dashboard(
        self,
        strategy_names: List[str],
        symbol: Optional[str] = None
    ) -> go.Figure:
        """Create interactive performance dashboard."""
        
        # Get data for all strategies
        all_data = []
        for strategy_name in strategy_names:
            data = self.get_strategy_history(strategy_name, symbol, limit=50)
            if not data.empty:
                data['strategy_name'] = strategy_name
                all_data.append(data)
        
        if not all_data:
            return go.Figure()
        
        combined_data = pd.concat(all_data, ignore_index=True)
        combined_data['test_date'] = pd.to_datetime(combined_data['test_date'])
        
        # Create subplots
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('Sharpe Ratio Over Time', 'Total Return Over Time',
                          'Max Drawdown Over Time', 'Win Rate Over Time'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        # Plot metrics for each strategy
        metrics = [
            ('sharpe_ratio', 1, 1),
            ('total_return', 1, 2),
            ('max_drawdown', 2, 1),
            ('win_rate', 2, 2)
        ]
        
        colors = ['blue', 'red', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']
        
        for i, strategy_name in enumerate(strategy_names):
            strategy_data = combined_data[combined_data['strategy_name'] == strategy_name]
            color = colors[i % len(colors)]
            
            for metric, row, col in metrics:
                fig.add_trace(
                    go.Scatter(
                        x=strategy_data['test_date'],
                        y=strategy_data[metric],
                        mode='lines+markers',
                        name=f"{strategy_name}_{metric}",
                        line=dict(color=color),
                        showlegend=(metric == 'sharpe_ratio')  # Only show legend for first metric
                    ),
                    row=row, col=col
                )
        
        # Update layout
        fig.update_layout(
            title="Strategy Performance Dashboard",
            height=800,
            showlegend=True
        )
        
        return fig
    
    def export_performance_data(
        self,
        filename: str,
        strategy_name: Optional[str] = None,
        symbol: Optional[str] = None,
        format: str = 'csv'
    ):
        """Export performance data to file."""
        conn = sqlite3.connect(self.db_path)
        
        query = "SELECT * FROM strategy_performance WHERE 1=1"
        params = []
        
        if strategy_name:
            query += " AND strategy_name = ?"
            params.append(strategy_name)
        
        if symbol:
            query += " AND symbol = ?"
            params.append(symbol)
        
        df = pd.read_sql_query(query, conn, params=params)
        conn.close()
        
        if format.lower() == 'csv':
            df.to_csv(filename, index=False)
        elif format.lower() == 'json':
            df.to_json(filename, orient='records', date_format='iso')
        elif format.lower() == 'excel':
            df.to_excel(filename, index=False)
        else:
            raise ValueError(f"Unsupported format: {format}")
        
        logger.info(f"Performance data exported to {filename}")
    
    def cleanup_old_records(self, days_to_keep: int = 90):
        """Clean up old performance records."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Delete old records
        cursor.execute("""
            DELETE FROM strategy_performance 
            WHERE test_date < datetime('now', '-{} days')
        """.format(days_to_keep))
        
        deleted_count = cursor.rowcount
        
        # Clean up orphaned trade details
        cursor.execute("""
            DELETE FROM trade_details 
            WHERE performance_id NOT IN (
                SELECT id FROM strategy_performance
            )
        """)
        
        conn.commit()
        conn.close()
        
        logger.info(f"Cleaned up {deleted_count} old performance records")
