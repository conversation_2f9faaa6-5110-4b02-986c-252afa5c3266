"""
Simple installation script for the cryptocurrency trading framework.
Installs only the essential dependencies needed to run the framework.
"""
import sys
import subprocess
import os


def install_package(package):
    """Install a single package."""
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
        return True
    except subprocess.CalledProcessError:
        return False


def main():
    """Install essential packages one by one."""
    print("🚀 Installing Cryptocurrency Trading Framework Dependencies")
    print("=" * 60)
    
    # Essential packages only
    essential_packages = [
        'numpy>=1.21.0',
        'pandas>=1.5.0', 
        'numba>=0.56.0',
        'scipy>=1.9.0',
        'python-binance>=1.0.17',
        'sqlalchemy>=1.4.0',
        'python-dotenv>=0.19.0',
        'pydantic>=1.10.0',
        'loguru>=0.6.0',
        'tqdm>=4.60.0'
    ]
    
    print(f"Installing {len(essential_packages)} essential packages...")
    
    installed = 0
    failed = []
    
    for package in essential_packages:
        package_name = package.split('>=')[0]
        print(f"Installing {package_name}...", end=' ')
        
        if install_package(package):
            print("✅")
            installed += 1
        else:
            print("❌")
            failed.append(package_name)
    
    print(f"\n📊 Installation Summary:")
    print(f"   ✅ Installed: {installed}/{len(essential_packages)}")
    
    if failed:
        print(f"   ❌ Failed: {len(failed)}")
        print(f"   Failed packages: {', '.join(failed)}")
    
    if installed >= len(essential_packages) - 2:  # Allow 2 failures
        print("\n🎉 Installation successful! Framework is ready to use.")
        print("\nNext steps:")
        print("   1. Configure your Binance API credentials in .env file")
        print("   2. Run: python start.py")
        print("   3. Or run: python verify_framework.py")
        return True
    else:
        print("\n⚠️ Too many packages failed to install.")
        print("Please install them manually or check your Python environment.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
