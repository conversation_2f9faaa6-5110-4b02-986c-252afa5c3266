"""
Fix database issues by recreating the database with correct schema.
"""
import os
import sys
import sqlite3

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def clean_database():
    """Remove existing database to force recreation."""

    print("🔧 FIXING DATABASE ISSUES")
    print("=" * 50)

    db_files = [
        "trading_data.db",
        "trading_data.db-journal",
        "trading_data.db-wal",
        "trading_data.db-shm"
    ]

    removed_count = 0
    for db_file in db_files:
        if os.path.exists(db_file):
            try:
                os.remove(db_file)
                print(f"   ✅ Removed {db_file}")
                removed_count += 1
            except Exception as e:
                print(f"   ⚠️ Could not remove {db_file}: {e}")

    if removed_count > 0:
        print(f"   🗑️ Cleaned {removed_count} database files")
    else:
        print("   ℹ️ No database files found to clean")

    return True

def test_database_creation():
    """Test database creation with correct schema."""

    print("\n📊 TESTING DATABASE CREATION")
    print("=" * 50)

    try:
        from core.data_manager import DataManager

        # Create data manager (this will create new database)
        print("   🔧 Creating DataManager...")
        data_manager = DataManager()
        print("   ✅ DataManager created successfully")

        # Test demo data generation and saving
        print("   📈 Testing demo data generation...")
        demo_data = data_manager._generate_demo_data(
            "BTCUSDT", "1h", "2024-01-01", "2024-01-02"
        )

        if not demo_data.empty:
            print(f"   ✅ Demo data generated: {len(demo_data)} rows")

            # Test saving to database
            print("   💾 Testing database save...")
            data_manager._save_to_database(demo_data, "BTCUSDT", "1h")
            print("   ✅ Data saved to database without errors")

            return True
        else:
            print("   ❌ Demo data generation failed")
            return False

    except Exception as e:
        print(f"   ❌ Database test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_database_schema():
    """Verify the database schema is correct."""

    print("\n🔍 VERIFYING DATABASE SCHEMA")
    print("=" * 50)

    try:
        conn = sqlite3.connect("trading_data.db")
        cursor = conn.cursor()

        # Get table info
        cursor.execute("PRAGMA table_info(klines)")
        columns = cursor.fetchall()

        print("   📋 Database columns:")
        for col in columns:
            col_name = col[1]
            col_type = col[2]
            not_null = "NOT NULL" if col[3] else "NULL"
            print(f"      {col_name}: {col_type} ({not_null})")

        # Check if close_time exists and is NOT NULL
        close_time_col = next((col for col in columns if col[1] == 'close_time'), None)

        if close_time_col and close_time_col[3]:  # NOT NULL = 1
            print("   ✅ close_time column exists and is NOT NULL")

            # Test a simple query
            cursor.execute("SELECT COUNT(*) FROM klines")
            count = cursor.fetchone()[0]
            print(f"   📊 Database contains {count} records")

            conn.close()
            return True
        else:
            print("   ❌ close_time column issue")
            conn.close()
            return False

    except Exception as e:
        print(f"   ❌ Schema verification failed: {e}")
        return False

def test_full_workflow():
    """Test the complete workflow without database errors."""

    print("\n🚀 TESTING FULL WORKFLOW")
    print("=" * 50)

    try:
        from core.data_manager import DataManager
        from strategies.technical_strategies import MovingAverageCrossover
        from core.backtester import VectorizedBacktester

        # Create components
        print("   🔧 Creating components...")
        data_manager = DataManager()
        strategy = MovingAverageCrossover("BTCUSDT", "1h", fast_period=10, slow_period=30)
        backtester = VectorizedBacktester(initial_capital=10000)
        print("   ✅ Components created")

        # Get data (should use demo data without database errors)
        print("   📊 Getting historical data...")
        data = data_manager.get_historical_data("BTCUSDT", "1h", "2024-01-01", "2024-01-10")  # More days

        if not data.empty:
            print(f"   ✅ Data retrieved: {len(data)} points")

            # Run backtest
            print("   🔬 Running backtest...")
            result = backtester.run_backtest(strategy, data)
            print("   ✅ Backtest completed successfully")
            print(f"   📊 Total Return: {result.total_return:.2%}")
            print(f"   📊 Total Trades: {result.total_trades}")

            return True
        else:
            print("   ❌ No data retrieved")
            return False

    except Exception as e:
        print(f"   ❌ Workflow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main fix function."""

    print("🛠️ DATABASE FIX UTILITY")
    print("=" * 60)
    print("This will fix the 'NOT NULL constraint failed: klines.close_time' error")
    print()

    tests = [
        ("Clean Database", clean_database),
        ("Test Database Creation", test_database_creation),
        ("Verify Schema", verify_database_schema),
        ("Test Full Workflow", test_full_workflow)
    ]

    passed = 0
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")

    print(f"\n📊 RESULTS: {passed}/{len(tests)} tests passed")

    if passed == len(tests):
        print("\n🎉 DATABASE ISSUES FIXED!")
        print("✅ Database schema corrected")
        print("✅ close_time constraint resolved")
        print("✅ Data saving working properly")
        print("✅ Full workflow operational")

        print("\n🚀 READY TO RUN:")
        print("   python main.py              # Should work without DB errors")
        print("   python evolution_runner.py  # Evolution system")
        print("   python test_binance_api.py  # API test")

        return True
    else:
        print(f"\n⚠️ {len(tests)-passed} tests failed")
        print("Some database issues may persist.")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
