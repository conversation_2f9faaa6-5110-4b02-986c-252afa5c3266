"""
Main execution script for the cryptocurrency trading framework.
Demonstrates framework usage with strategy optimization and backtesting.
"""
import time
from datetime import datetime, timedelta
import pandas as pd
from loguru import logger
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.data_manager import DataManager
from core.backtester import VectorizedBacktester
from core.optimizer import StrategyOptimizer
from strategies.technical_strategies import (
    MovingAverageCrossover,
    RSIMeanReversion,
    BollingerBandsMomentum,
    MACDStrategy,
    AdvancedMomentumStrategy
)
from utils.performance_tracker import PerformanceTracker
from config.settings import settings


class TradingFramework:
    """
    Main trading framework orchestrator.
    Manages data, strategies, optimization, and performance tracking.
    """

    def __init__(self):
        self.data_manager = DataManager()
        self.backtester = VectorizedBacktester(initial_capital=10000.0)
        self.optimizer = StrategyOptimizer(
            self.data_manager,
            self.backtester,
            optimization_metric='sharpe_ratio'
        )
        self.performance_tracker = PerformanceTracker()

        # Setup logging
        logger.add(
            settings.log_file,
            level=settings.log_level,
            rotation="1 day",
            retention="30 days",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
        )

        logger.info("Trading Framework initialized")

    def run_strategy_comparison(
        self,
        symbol: str = "BTCUSDT",
        timeframe: str = "1h",
        start_date: str = "2022-01-01",
        end_date: str = "2024-01-01"
    ):
        """
        Run comprehensive strategy comparison and optimization.

        Args:
            symbol: Trading symbol
            timeframe: Data timeframe
            start_date: Backtest start date
            end_date: Backtest end date
        """
        logger.info(f"Starting strategy comparison for {symbol}")

        # Get historical data
        logger.info("Fetching historical data...")
        data = self.data_manager.get_historical_data(
            symbol, timeframe, start_date, end_date
        )

        if data.empty:
            logger.error("No data available for the specified period")
            return

        logger.info(f"Loaded {len(data)} data points from {data.index[0]} to {data.index[-1]}")

        # Define strategies to test with optimized parameters
        strategies_config = [
            {
                'class': MovingAverageCrossover,
                'name': 'MA_Crossover',
                'params': {'fast_period': 8, 'slow_period': 21, 'trend_period': 50, 'min_trend_strength': 0.005},
                'param_ranges': {
                    'fast_period': (5, 15),
                    'slow_period': (15, 35),
                    'trend_period': (30, 100),
                    'min_trend_strength': (0.001, 0.02)
                }
            },
            {
                'class': RSIMeanReversion,
                'name': 'RSI_MeanReversion',
                'params': {'rsi_period': 12, 'oversold_threshold': 35, 'overbought_threshold': 65, 'trend_filter': False},
                'param_ranges': {
                    'rsi_period': (8, 18),
                    'oversold_threshold': (25, 40),
                    'overbought_threshold': (60, 75)
                }
            },
            {
                'class': BollingerBandsMomentum,
                'name': 'BB_Momentum',
                'params': {'bb_period': 18, 'bb_std': 1.8, 'volume_threshold': 1.2, 'momentum_period': 8},
                'param_ranges': {
                    'bb_period': (12, 25),
                    'bb_std': (1.2, 2.2),
                    'volume_threshold': (1.0, 1.8),
                    'momentum_period': (5, 15)
                }
            },
            {
                'class': MACDStrategy,
                'name': 'MACD',
                'params': {'fast_period': 10, 'slow_period': 22, 'signal_period': 7, 'histogram_threshold': 0.0},
                'param_ranges': {
                    'fast_period': (6, 14),
                    'slow_period': (18, 30),
                    'signal_period': (5, 10),
                    'histogram_threshold': (-0.001, 0.001)
                }
            },
            {
                'class': AdvancedMomentumStrategy,
                'name': 'Advanced_Momentum',
                'params': {'momentum_period': 12, 'roc_period': 8, 'volatility_period': 16, 'min_momentum_threshold': 0.008},
                'param_ranges': {
                    'momentum_period': (8, 20),
                    'roc_period': (5, 15),
                    'volatility_period': (10, 25),
                    'min_momentum_threshold': (0.003, 0.015)
                }
            }
        ]

        results = {}

        # Test each strategy
        for strategy_config in strategies_config:
            logger.info(f"\n{'='*60}")
            logger.info(f"Testing {strategy_config['name']}")
            logger.info(f"{'='*60}")

            try:
                # 1. Baseline test with default parameters
                logger.info("Running baseline backtest...")
                baseline_strategy = strategy_config['class'](
                    symbol, timeframe, **strategy_config['params']
                )
                baseline_result = self.backtester.run_backtest(baseline_strategy, data)

                logger.info(f"Baseline Results:")
                logger.info(f"  Total Return: {baseline_result.total_return:.2%}")
                logger.info(f"  Sharpe Ratio: {baseline_result.sharpe_ratio:.4f}")
                logger.info(f"  Max Drawdown: {baseline_result.max_drawdown:.2%}")
                logger.info(f"  Win Rate: {baseline_result.win_rate:.2%}")
                logger.info(f"  Total Trades: {baseline_result.total_trades}")

                # 2. Parameter optimization
                logger.info("Starting parameter optimization...")
                optimization_result = self.optimizer.optimize_strategy(
                    strategy_config['class'],
                    symbol,
                    timeframe,
                    strategy_config['param_ranges'],
                    start_date,
                    end_date,
                    method='bayesian',
                    n_trials=50
                )

                logger.info(f"Optimization Results:")
                logger.info(f"  Best Parameters: {optimization_result.best_parameters}")
                logger.info(f"  Best Score: {optimization_result.best_score:.4f}")
                logger.info(f"  Robustness Score: {optimization_result.robustness_score:.4f}")

                # 3. Test optimized strategy
                logger.info("Testing optimized strategy...")
                optimized_strategy = strategy_config['class'](
                    symbol, timeframe, **optimization_result.best_parameters
                )
                optimized_result = self.backtester.run_backtest(optimized_strategy, data)

                logger.info(f"Optimized Results:")
                logger.info(f"  Total Return: {optimized_result.total_return:.2%}")
                logger.info(f"  Sharpe Ratio: {optimized_result.sharpe_ratio:.4f}")
                logger.info(f"  Max Drawdown: {optimized_result.max_drawdown:.2%}")
                logger.info(f"  Win Rate: {optimized_result.win_rate:.2%}")
                logger.info(f"  Total Trades: {optimized_result.total_trades}")

                # Store results
                results[strategy_config['name']] = {
                    'baseline': baseline_result,
                    'optimized': optimized_result,
                    'optimization': optimization_result
                }

                # Track performance
                self.performance_tracker.add_result(
                    strategy_config['name'],
                    optimized_result,
                    optimization_result.best_parameters
                )

            except Exception as e:
                logger.error(f"Error testing {strategy_config['name']}: {e}")
                continue

        # Generate comparison report
        self._generate_comparison_report(results, symbol, timeframe)

        return results

    def _generate_comparison_report(
        self,
        results: dict,
        symbol: str,
        timeframe: str
    ):
        """Generate comprehensive comparison report."""
        logger.info(f"\n{'='*80}")
        logger.info("STRATEGY COMPARISON REPORT")
        logger.info(f"{'='*80}")
        logger.info(f"Symbol: {symbol} | Timeframe: {timeframe}")
        logger.info(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # Create comparison table
        comparison_data = []

        for strategy_name, strategy_results in results.items():
            baseline = strategy_results['baseline']
            optimized = strategy_results['optimized']
            optimization = strategy_results['optimization']

            comparison_data.append({
                'Strategy': strategy_name,
                'Baseline_Return': f"{baseline.total_return:.2%}",
                'Optimized_Return': f"{optimized.total_return:.2%}",
                'Improvement': f"{((optimized.total_return - baseline.total_return) / abs(baseline.total_return) * 100):.1f}%",
                'Baseline_Sharpe': f"{baseline.sharpe_ratio:.4f}",
                'Optimized_Sharpe': f"{optimized.sharpe_ratio:.4f}",
                'Baseline_MaxDD': f"{baseline.max_drawdown:.2%}",
                'Optimized_MaxDD': f"{optimized.max_drawdown:.2%}",
                'Win_Rate': f"{optimized.win_rate:.2%}",
                'Total_Trades': optimized.total_trades,
                'Robustness': f"{optimization.robustness_score:.4f}",
                'Exec_Time': f"{optimized.execution_time:.4f}s"
            })

        # Display table
        df = pd.DataFrame(comparison_data)
        logger.info("\nPerformance Comparison:")
        logger.info(f"\n{df.to_string(index=False)}")

        # Find best strategy
        if results:
            best_strategy = max(
                results.items(),
                key=lambda x: x[1]['optimized'].sharpe_ratio * x[1]['optimization'].robustness_score
            )

            logger.info(f"\n🏆 BEST STRATEGY: {best_strategy[0]}")
            logger.info(f"   Optimized Sharpe Ratio: {best_strategy[1]['optimized'].sharpe_ratio:.4f}")
            logger.info(f"   Robustness Score: {best_strategy[1]['optimization'].robustness_score:.4f}")
            logger.info(f"   Total Return: {best_strategy[1]['optimized'].total_return:.2%}")
            logger.info(f"   Max Drawdown: {best_strategy[1]['optimized'].max_drawdown:.2%}")
            logger.info(f"   Best Parameters: {best_strategy[1]['optimization'].best_parameters}")

        logger.info(f"\n{'='*80}")

    def _generate_final_report(self, all_results: dict):
        """Generate comprehensive final report across all tests."""
        logger.info(f"\n{'='*100}")
        logger.info("🏆 COMPREHENSIVE TRADING FRAMEWORK RESULTS")
        logger.info(f"{'='*100}")
        logger.info(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # Aggregate statistics
        total_tests = sum(len(results) for results in all_results.values())
        successful_tests = sum(1 for results in all_results.values() for _ in results if results)

        logger.info(f"\n📊 SUMMARY STATISTICS")
        logger.info(f"   Total Symbol/Timeframe Combinations: {len(all_results)}")
        logger.info(f"   Total Strategy Tests: {total_tests}")
        logger.info(f"   Successful Tests: {successful_tests}")

        # Best performers by metric
        all_strategies = []
        for symbol_tf, results in all_results.items():
            symbol, timeframe = symbol_tf.split('_', 1)
            for strategy_name, strategy_data in results.items():
                optimized = strategy_data['optimized']
                optimization = strategy_data['optimization']

                all_strategies.append({
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'strategy': strategy_name,
                    'sharpe': optimized.sharpe_ratio,
                    'return': optimized.total_return,
                    'max_dd': optimized.max_drawdown,
                    'win_rate': optimized.win_rate,
                    'trades': optimized.total_trades,
                    'robustness': optimization.robustness_score,
                    'exec_time': optimized.execution_time
                })

        if all_strategies:
            # Sort by combined score (Sharpe * Robustness)
            all_strategies.sort(key=lambda x: x['sharpe'] * x['robustness'], reverse=True)

            logger.info(f"\n🥇 TOP 5 STRATEGIES (by Sharpe × Robustness):")
            for i, strategy in enumerate(all_strategies[:5], 1):
                score = strategy['sharpe'] * strategy['robustness']
                logger.info(f"   {i}. {strategy['strategy']} ({strategy['symbol']}-{strategy['timeframe']})")
                logger.info(f"      Score: {score:.4f} | Sharpe: {strategy['sharpe']:.4f} | Return: {strategy['return']:.2%}")
                logger.info(f"      Robustness: {strategy['robustness']:.4f} | Max DD: {strategy['max_dd']:.2%}")

            # Performance by symbol
            logger.info(f"\n📈 PERFORMANCE BY SYMBOL:")
            symbols = set(s['symbol'] for s in all_strategies)
            for symbol in sorted(symbols):
                symbol_strategies = [s for s in all_strategies if s['symbol'] == symbol]
                best = max(symbol_strategies, key=lambda x: x['sharpe'])
                avg_return = sum(s['return'] for s in symbol_strategies) / len(symbol_strategies)

                logger.info(f"   {symbol}:")
                logger.info(f"      Best Strategy: {best['strategy']} (Sharpe: {best['sharpe']:.4f})")
                logger.info(f"      Average Return: {avg_return:.2%}")
                logger.info(f"      Tests: {len(symbol_strategies)}")

            # Performance by timeframe
            logger.info(f"\n⏰ PERFORMANCE BY TIMEFRAME:")
            timeframes = set(s['timeframe'] for s in all_strategies)
            for tf in sorted(timeframes):
                tf_strategies = [s for s in all_strategies if s['timeframe'] == tf]
                best = max(tf_strategies, key=lambda x: x['sharpe'])
                avg_return = sum(s['return'] for s in tf_strategies) / len(tf_strategies)

                logger.info(f"   {tf}:")
                logger.info(f"      Best Strategy: {best['strategy']} (Sharpe: {best['sharpe']:.4f})")
                logger.info(f"      Average Return: {avg_return:.2%}")
                logger.info(f"      Tests: {len(tf_strategies)}")

    def _find_best_strategy(self, all_results: dict) -> dict:
        """Find the best overall strategy across all tests."""
        best_strategy = None
        best_score = -float('inf')

        for symbol_tf, results in all_results.items():
            symbol, timeframe = symbol_tf.split('_', 1)
            for strategy_name, strategy_data in results.items():
                optimized = strategy_data['optimized']
                optimization = strategy_data['optimization']

                # Combined score: Sharpe * Robustness * (1 - MaxDD)
                score = (optimized.sharpe_ratio *
                        optimization.robustness_score *
                        (1 - optimized.max_drawdown))

                if score > best_score:
                    best_score = score
                    best_strategy = {
                        'name': strategy_name,
                        'symbol': symbol,
                        'timeframe': timeframe,
                        'sharpe': optimized.sharpe_ratio,
                        'return': optimized.total_return,
                        'max_dd': optimized.max_drawdown,
                        'robustness': optimization.robustness_score,
                        'parameters': optimization.best_parameters,
                        'score': score
                    }

        return best_strategy

    def run_live_simulation(
        self,
        strategy_class,
        parameters: dict,
        symbol: str = "BTCUSDT",
        timeframe: str = "1h",
        duration_hours: int = 24
    ):
        """
        Run live simulation with real-time data updates.

        Args:
            strategy_class: Strategy class to run
            parameters: Strategy parameters
            symbol: Trading symbol
            timeframe: Data timeframe
            duration_hours: Simulation duration in hours
        """
        logger.info(f"Starting live simulation for {duration_hours} hours")

        strategy = strategy_class(symbol, timeframe, **parameters)

        # Initialize with historical data
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)

        data = self.data_manager.get_historical_data(
            symbol, timeframe,
            start_date.strftime('%Y-%m-%d'),
            end_date.strftime('%Y-%m-%d')
        )

        strategy.initialize(data)

        # Simulation loop
        start_time = time.time()

        while time.time() - start_time < duration_hours * 3600:
            try:
                # Get latest price
                current_price = self.data_manager.get_latest_price(symbol)

                # Update data (simplified - in real implementation, you'd get latest kline)
                latest_data = data.copy()

                # Generate signal
                signal = strategy.process_data(latest_data)

                if signal.action != 'HOLD':
                    logger.info(f"Signal: {signal.action} at {current_price} "
                               f"(confidence: {signal.confidence:.2f})")

                # Update positions
                closed_positions = strategy.update_positions(current_price)

                for position in closed_positions:
                    logger.info(f"Position closed: {position.unrealized_pnl:.2f} PnL")

                # Wait for next update (adjust based on timeframe)
                time.sleep(60)  # 1 minute updates

            except KeyboardInterrupt:
                logger.info("Simulation stopped by user")
                break
            except Exception as e:
                logger.error(f"Simulation error: {e}")
                time.sleep(60)

        # Final performance report
        performance = strategy.get_performance_metrics()
        logger.info("Live Simulation Results:")
        for metric, value in performance.items():
            logger.info(f"  {metric}: {value}")


def main():
    """Main execution function - fully automated with real data."""
    logger.info("🚀 Starting Cryptocurrency Trading Framework")
    logger.info("=" * 80)

    framework = TradingFramework()

    # Test multiple symbols and timeframes automatically
    test_configs = [
        {"symbol": "BTCUSDT", "timeframe": "1h", "start": "2022-01-01", "end": "2024-01-01"},
        {"symbol": "ETHUSDT", "timeframe": "1h", "start": "2023-01-01", "end": "2024-01-01"},
        {"symbol": "BTCUSDT", "timeframe": "4h", "start": "2022-01-01", "end": "2024-01-01"},
    ]

    all_results = {}

    for config in test_configs:
        logger.info(f"\n🔍 Testing {config['symbol']} - {config['timeframe']}")
        logger.info("-" * 60)

        try:
            results = framework.run_strategy_comparison(
                symbol=config["symbol"],
                timeframe=config["timeframe"],
                start_date=config["start"],
                end_date=config["end"]
            )

            if results:
                all_results[f"{config['symbol']}_{config['timeframe']}"] = results
                logger.info(f"✅ Completed {config['symbol']} - {config['timeframe']}")
            else:
                logger.warning(f"⚠️ No results for {config['symbol']} - {config['timeframe']}")

        except Exception as e:
            logger.error(f"❌ Error testing {config['symbol']}: {e}")
            continue

    # Generate final comprehensive report
    if all_results:
        framework._generate_final_report(all_results)

        # Auto-select and test best strategy
        best_overall = framework._find_best_strategy(all_results)
        if best_overall:
            logger.info(f"\n🏆 BEST OVERALL STRATEGY: {best_overall['name']}")
            logger.info(f"   Symbol: {best_overall['symbol']}")
            logger.info(f"   Sharpe Ratio: {best_overall['sharpe']:.4f}")
            logger.info(f"   Total Return: {best_overall['return']:.2%}")
            logger.info(f"   Robustness: {best_overall['robustness']:.4f}")
    else:
        logger.error("❌ No successful results generated")

    logger.info("\n🎯 Framework execution completed!")


if __name__ == "__main__":
    main()
