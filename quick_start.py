"""
Quick start guide for the cryptocurrency trading framework.
This script demonstrates the most important features in a simple way.
"""
import sys
import os
import time
import pandas as pd
import numpy as np

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_sample_data():
    """Create sample market data for demonstration."""
    print("📊 Creating sample market data...")
    
    # Generate realistic price data
    np.random.seed(42)  # For reproducible results
    
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='1H')
    
    # Simulate price movement with trend and volatility
    returns = np.random.normal(0.0001, 0.02, len(dates))  # Small positive drift
    prices = 30000 * np.exp(np.cumsum(returns))  # Starting at $30,000
    
    # Create OHLCV data
    data = pd.DataFrame(index=dates)
    data['close'] = prices
    data['open'] = data['close'].shift(1).fillna(data['close'].iloc[0])
    data['high'] = np.maximum(data['open'], data['close']) * (1 + np.random.uniform(0, 0.01, len(data)))
    data['low'] = np.minimum(data['open'], data['close']) * (1 - np.random.uniform(0, 0.01, len(data)))
    data['volume'] = np.random.uniform(100, 1000, len(data))
    
    print(f"✅ Generated {len(data)} data points from {data.index[0]} to {data.index[-1]}")
    return data


def demo_strategy_creation():
    """Demonstrate how to create and use a trading strategy."""
    print("\n🎯 DEMO 1: Creating and Testing a Strategy")
    print("-" * 50)
    
    from strategies.technical_strategies import MovingAverageCrossover
    
    # Create strategy
    strategy = MovingAverageCrossover(
        symbol="BTCUSDT",
        timeframe="1h",
        fast_period=10,
        slow_period=30,
        trend_period=100
    )
    
    print(f"✅ Created strategy: {strategy.name}")
    print(f"   Symbol: {strategy.symbol}")
    print(f"   Parameters: {strategy.parameters}")
    
    # Generate sample data
    data = create_sample_data()
    
    # Initialize strategy
    strategy.initialize(data.head(100))
    
    # Generate a signal
    signal = strategy.process_data(data.head(200))
    
    print(f"\n📡 Generated signal:")
    print(f"   Action: {signal.action}")
    print(f"   Price: ${signal.price:.2f}")
    print(f"   Confidence: {signal.confidence:.2f}")
    print(f"   Metadata: {signal.metadata}")


def demo_backtesting():
    """Demonstrate backtesting functionality."""
    print("\n🔬 DEMO 2: Backtesting a Strategy")
    print("-" * 50)
    
    from strategies.technical_strategies import RSIMeanReversion
    from core.backtester import VectorizedBacktester
    
    # Create strategy and backtester
    strategy = RSIMeanReversion(
        symbol="BTCUSDT",
        timeframe="1h",
        rsi_period=14,
        oversold_threshold=30,
        overbought_threshold=70
    )
    
    backtester = VectorizedBacktester(initial_capital=10000)
    
    # Generate sample data
    data = create_sample_data()
    
    print("🔄 Running backtest...")
    start_time = time.time()
    
    result = backtester.run_backtest(strategy, data)
    
    execution_time = time.time() - start_time
    
    print(f"✅ Backtest completed in {execution_time:.4f} seconds")
    print(f"\n📊 Results:")
    print(f"   💰 Initial Capital: ${result.initial_capital:,.2f}")
    print(f"   💰 Final Capital: ${result.final_capital:,.2f}")
    print(f"   📈 Total Return: {result.total_return:.2%}")
    print(f"   ⚡ Sharpe Ratio: {result.sharpe_ratio:.4f}")
    print(f"   📉 Max Drawdown: {result.max_drawdown:.2%}")
    print(f"   🎯 Win Rate: {result.win_rate:.2%}")
    print(f"   🔄 Total Trades: {result.total_trades}")


def demo_optimization():
    """Demonstrate strategy optimization."""
    print("\n🧬 DEMO 3: Strategy Optimization")
    print("-" * 50)
    
    from strategies.technical_strategies import MovingAverageCrossover
    from core.backtester import VectorizedBacktester
    from core.data_manager import DataManager
    from core.optimizer import StrategyOptimizer
    
    # Create components (using sample data since we don't have API access)
    backtester = VectorizedBacktester(initial_capital=10000)
    
    # For this demo, we'll simulate the optimization process
    print("🔍 Simulating parameter optimization...")
    
    # Define parameter ranges
    parameter_ranges = {
        'fast_period': (5, 15),
        'slow_period': (20, 40),
        'trend_period': (50, 150)
    }
    
    print(f"   Parameter ranges: {parameter_ranges}")
    
    # Simulate optimization results
    best_params = {
        'fast_period': 8,
        'slow_period': 25,
        'trend_period': 75
    }
    
    best_score = 1.25  # Simulated Sharpe ratio
    
    print(f"✅ Optimization completed!")
    print(f"   🏆 Best Parameters: {best_params}")
    print(f"   📊 Best Score: {best_score:.4f}")
    
    # Test optimized strategy
    optimized_strategy = MovingAverageCrossover(
        symbol="BTCUSDT",
        timeframe="1h",
        **best_params
    )
    
    data = create_sample_data()
    result = backtester.run_backtest(optimized_strategy, data)
    
    print(f"   📈 Optimized Return: {result.total_return:.2%}")
    print(f"   ⚡ Optimized Sharpe: {result.sharpe_ratio:.4f}")


def demo_performance_tracking():
    """Demonstrate performance tracking."""
    print("\n📊 DEMO 4: Performance Tracking")
    print("-" * 50)
    
    from utils.performance_tracker import PerformanceTracker
    from core.backtester import BacktestResult
    
    # Create performance tracker
    tracker = PerformanceTracker(db_path="demo_performance.db")
    
    # Simulate some backtest results
    print("📝 Adding sample performance records...")
    
    sample_results = [
        {
            'strategy_name': 'MA_Crossover',
            'total_return': 0.15,
            'sharpe_ratio': 1.2,
            'max_drawdown': 0.08,
            'win_rate': 0.65,
            'total_trades': 45
        },
        {
            'strategy_name': 'RSI_MeanReversion',
            'total_return': 0.22,
            'sharpe_ratio': 1.5,
            'max_drawdown': 0.12,
            'win_rate': 0.58,
            'total_trades': 67
        },
        {
            'strategy_name': 'BB_Momentum',
            'total_return': 0.08,
            'sharpe_ratio': 0.9,
            'max_drawdown': 0.06,
            'win_rate': 0.72,
            'total_trades': 23
        }
    ]
    
    for i, result_data in enumerate(sample_results):
        # Create a mock BacktestResult
        result = type('MockResult', (), {
            'strategy_name': result_data['strategy_name'],
            'symbol': 'BTCUSDT',
            'timeframe': '1h',
            'start_date': '2023-01-01',
            'end_date': '2023-12-31',
            'initial_capital': 10000,
            'final_capital': 10000 * (1 + result_data['total_return']),
            'total_return': result_data['total_return'],
            'annual_return': result_data['total_return'],
            'sharpe_ratio': result_data['sharpe_ratio'],
            'sortino_ratio': result_data['sharpe_ratio'] * 1.1,
            'calmar_ratio': result_data['sharpe_ratio'] * 0.8,
            'max_drawdown': result_data['max_drawdown'],
            'win_rate': result_data['win_rate'],
            'profit_factor': 1.5,
            'total_trades': result_data['total_trades'],
            'avg_trade_duration': 24.5,
            'execution_time': 0.05,
            'trades': []
        })()
        
        tracker.add_result(
            strategy_name=result_data['strategy_name'],
            result=result,
            parameters={'demo': True},
            robustness_score=0.8
        )
    
    print("✅ Added performance records")
    
    # Get best strategies
    best_strategies = tracker.get_best_strategies(metric='sharpe_ratio', limit=3)
    
    print(f"\n🏆 Top Strategies by Sharpe Ratio:")
    for _, row in best_strategies.iterrows():
        print(f"   {row['strategy_name']}: {row['sharpe_ratio']:.4f}")
    
    # Generate report
    report = tracker.generate_performance_report(days_back=365)
    print(f"\n📋 Performance Report Preview:")
    print(report[:500] + "..." if len(report) > 500 else report)


def demo_metrics():
    """Demonstrate advanced metrics calculation."""
    print("\n📈 DEMO 5: Advanced Metrics")
    print("-" * 50)
    
    from utils.metrics import AdvancedMetrics, calculate_sharpe_ratio
    import numpy as np
    
    # Generate sample returns
    np.random.seed(42)
    returns = np.random.normal(0.001, 0.02, 252)  # One year of daily returns
    equity_curve = 10000 * np.cumprod(1 + returns)
    
    print("📊 Calculating comprehensive metrics...")
    
    # Calculate all metrics
    metrics = AdvancedMetrics.calculate_all_metrics(
        returns=returns,
        equity_curve=equity_curve,
        risk_free_rate=0.02
    )
    
    print("✅ Metrics calculated:")
    for metric_name, value in metrics.items():
        if isinstance(value, float):
            if 'ratio' in metric_name or 'return' in metric_name:
                print(f"   {metric_name.replace('_', ' ').title()}: {value:.4f}")
            elif 'drawdown' in metric_name:
                print(f"   {metric_name.replace('_', ' ').title()}: {value:.2%}")
            else:
                print(f"   {metric_name.replace('_', ' ').title()}: {value:.4f}")


def main():
    """Run all demonstrations."""
    print("🚀 Cryptocurrency Trading Framework - Quick Start Guide")
    print("=" * 70)
    print("This guide demonstrates the key features of the framework:")
    print("  ⚡ Ultra-fast strategy execution")
    print("  🛡️  Robust backtesting engine")
    print("  🧬 Advanced optimization algorithms")
    print("  📊 Comprehensive performance tracking")
    print("  📈 Advanced risk metrics")
    
    demos = [
        demo_strategy_creation,
        demo_backtesting,
        demo_optimization,
        demo_performance_tracking,
        demo_metrics
    ]
    
    for i, demo in enumerate(demos, 1):
        try:
            demo()
            if i < len(demos):
                input(f"\nPress Enter to continue to Demo {i+1}...")
        except Exception as e:
            print(f"❌ Demo {i} failed: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 70)
    print("🎉 Quick Start Guide Completed!")
    print("\nNext Steps:")
    print("1. 🔧 Configure your Binance API credentials in .env")
    print("2. 🚀 Run the full framework: python main.py")
    print("3. 🧪 Try the interactive examples: python run_example.py")
    print("4. 📚 Read the README.md for detailed documentation")
    print("5. 🛠️  Create your own strategies by extending BaseStrategy")
    
    print("\n💡 Pro Tips:")
    print("  • Use JIT compilation for maximum speed")
    print("  • Always validate strategies with walk-forward analysis")
    print("  • Monitor robustness scores for real-world performance")
    print("  • Compare multiple strategies before going live")
    
    # Cleanup demo database
    try:
        os.remove("demo_performance.db")
        print("\n🧹 Cleaned up demo files")
    except:
        pass


if __name__ == "__main__":
    main()
