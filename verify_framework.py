#!/usr/bin/env python3
"""
Quick verification that the framework is working correctly.
"""
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def verify_framework():
    """Verify framework functionality."""
    print("🔍 Verifying Cryptocurrency Trading Framework...")
    
    # Test 1: Configuration
    try:
        from config.settings import settings
        print("✅ Configuration loaded")
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False
    
    # Test 2: Strategy import
    try:
        from strategies.technical_strategies import MovingAverageCrossover
        print("✅ Strategies imported")
    except Exception as e:
        print(f"❌ Strategy import error: {e}")
        return False
    
    # Test 3: Core components
    try:
        from core.backtester import VectorizedBacktester
        from utils.metrics import calculate_sharpe_ratio
        print("✅ Core components imported")
    except Exception as e:
        print(f"❌ Core components error: {e}")
        return False
    
    # Test 4: Create strategy
    try:
        strategy = MovingAverageCrossover("BTCUSDT", "1h")
        print("✅ Strategy created successfully")
    except Exception as e:
        print(f"❌ Strategy creation error: {e}")
        return False
    
    # Test 5: Create backtester
    try:
        backtester = VectorizedBacktester(initial_capital=10000)
        print("✅ Backtester created successfully")
    except Exception as e:
        print(f"❌ Backtester creation error: {e}")
        return False
    
    print("\n🎉 Framework verification PASSED!")
    print("\nFramework is ready for:")
    print("  ✅ Strategy development and testing")
    print("  ✅ Backtesting with historical data")
    print("  ✅ Performance optimization")
    print("  ✅ Automated execution")
    
    return True

if __name__ == "__main__":
    success = verify_framework()
    if success:
        print("\n🚀 Ready to run:")
        print("   python start.py      # Complete automated setup and execution")
        print("   python auto_run.py   # Advanced analysis with real data")
        print("   python main.py       # Main framework execution")
    else:
        print("\n❌ Framework verification failed")
    
    sys.exit(0 if success else 1)
