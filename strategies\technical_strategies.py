"""
High-performance technical analysis strategies optimized for speed and robustness.
Implements proven strategies with vectorized calculations.
"""
import numpy as np
import pandas as pd
try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    talib = None
from typing import Dict, Any, Optional
from numba import jit

from strategies.base_strategy import BaseStrategy, Signal
from config.settings import settings


def safe_sma(data: np.ndarray, period: int) -> np.ndarray:
    """Safe Simple Moving Average calculation - optimized."""
    if TALIB_AVAILABLE:
        return talib.SMA(data, timeperiod=period)
    else:
        # Optimized fallback using pandas rolling
        import pandas as pd
        if len(data) < period:
            return np.full(len(data), np.nan)

        series = pd.Series(data)
        sma = series.rolling(window=period, min_periods=period).mean()
        return sma.fillna(method='ffill').fillna(0).values


def safe_rsi(data: np.ndarray, period: int) -> np.ndarray:
    """Safe RSI calculation."""
    if TALIB_AVAILABLE:
        return talib.RSI(data, timeperiod=period)
    else:
        # Fallback implementation
        delta = np.diff(data)
        gain = np.where(delta > 0, delta, 0)
        loss = np.where(delta < 0, -delta, 0)

        avg_gain = np.full(len(data), np.nan)
        avg_loss = np.full(len(data), np.nan)

        # Initial averages
        if len(gain) >= period:
            avg_gain[period] = np.mean(gain[:period])
            avg_loss[period] = np.mean(loss[:period])

            # Smoothed averages
            for i in range(period + 1, len(data)):
                avg_gain[i] = (avg_gain[i-1] * (period - 1) + gain[i-1]) / period
                avg_loss[i] = (avg_loss[i-1] * (period - 1) + loss[i-1]) / period

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi


def safe_bbands(data: np.ndarray, period: int, std_dev: float) -> tuple:
    """Safe Bollinger Bands calculation."""
    if TALIB_AVAILABLE:
        return talib.BBANDS(data, timeperiod=period, nbdevup=std_dev, nbdevdn=std_dev, matype=0)
    else:
        # Fallback implementation
        sma = safe_sma(data, period)
        std = np.full(len(data), np.nan)

        for i in range(period - 1, len(data)):
            std[i] = np.std(data[i - period + 1:i + 1])

        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)

        return upper, sma, lower


def safe_macd(data: np.ndarray, fast: int, slow: int, signal: int) -> tuple:
    """Safe MACD calculation."""
    if TALIB_AVAILABLE:
        return talib.MACD(data, fastperiod=fast, slowperiod=slow, signalperiod=signal)
    else:
        # Fallback implementation
        ema_fast = calculate_ema_fast(data, fast)
        ema_slow = calculate_ema_fast(data, slow)
        macd_line = ema_fast - ema_slow
        signal_line = calculate_ema_fast(macd_line, signal)
        histogram = macd_line - signal_line

        return macd_line, signal_line, histogram


class MovingAverageCrossover(BaseStrategy):
    """
    Optimized Moving Average Crossover strategy.
    Uses fast and slow MA crossovers with trend confirmation.
    """

    def __init__(
        self,
        symbol: str,
        timeframe: str,
        fast_period: int = 10,
        slow_period: int = 30,
        trend_period: int = 100,
        min_trend_strength: float = 0.02
    ):
        parameters = {
            'fast_period': fast_period,
            'slow_period': slow_period,
            'trend_period': trend_period,
            'min_trend_strength': min_trend_strength
        }

        super().__init__(
            name=f"MA_Cross_{fast_period}_{slow_period}",
            symbol=symbol,
            timeframe=timeframe,
            parameters=parameters
        )

        self.fast_period = fast_period
        self.slow_period = slow_period
        self.trend_period = trend_period
        self.min_trend_strength = min_trend_strength

    def generate_signal(self, data: pd.DataFrame) -> Signal:
        """Generate signal based on MA crossover with adaptive thresholds."""
        if len(data) < max(self.slow_period, self.trend_period) + 1:
            return Signal(
                timestamp=data.index[-1],
                action='HOLD',
                price=data['close'].iloc[-1],
                confidence=0.0,
                metadata={'reason': 'insufficient_data'}
            )

        # Calculate moving averages
        fast_ma = safe_sma(data['close'].values, self.fast_period)
        slow_ma = safe_sma(data['close'].values, self.slow_period)
        trend_ma = safe_sma(data['close'].values, self.trend_period)

        # Current values
        current_price = data['close'].iloc[-1]
        current_fast = fast_ma[-1]
        current_slow = slow_ma[-1]
        current_trend = trend_ma[-1]

        # Previous values for crossover detection
        prev_fast = fast_ma[-2]
        prev_slow = slow_ma[-2]

        # Enhanced trend strength calculation
        trend_strength = (current_price - current_trend) / current_trend

        # Calculate volatility for adaptive thresholds
        close_prices = data['close'].values
        if len(close_prices) >= 20:
            price_window = close_prices[-20:]
            returns = np.diff(price_window) / price_window[:-1]
            volatility = np.std(returns) if len(returns) > 0 else 0.02
        else:
            volatility = 0.02

        # Adaptive minimum trend strength based on volatility
        adaptive_min_trend = self.min_trend_strength * (0.5 + volatility * 25)

        # Calculate MA separation for signal strength
        ma_separation = abs(current_fast - current_slow) / current_slow

        # Signal generation with improved logic
        action = 'HOLD'
        confidence = 0.0
        metadata = {
            'trend_strength': trend_strength,
            'volatility': volatility,
            'ma_separation': ma_separation,
            'adaptive_threshold': adaptive_min_trend
        }

        # Bullish crossover with enhanced conditions
        if (prev_fast <= prev_slow and current_fast > current_slow):
            # Base signal strength from crossover
            base_confidence = 0.4

            # Boost confidence with trend alignment
            if trend_strength > adaptive_min_trend:
                base_confidence += 0.3
            elif trend_strength > 0:
                base_confidence += 0.1

            # Boost confidence with MA separation
            base_confidence += min(0.3, ma_separation * 20)

            action = 'BUY'
            confidence = min(0.9, base_confidence)
            metadata['signal_type'] = 'bullish_crossover'

        # Bearish crossover with enhanced conditions
        elif (prev_fast >= prev_slow and current_fast < current_slow):
            # Base signal strength from crossover
            base_confidence = 0.4

            # Boost confidence with trend alignment
            if trend_strength < -adaptive_min_trend:
                base_confidence += 0.3
            elif trend_strength < 0:
                base_confidence += 0.1

            # Boost confidence with MA separation
            base_confidence += min(0.3, ma_separation * 20)

            action = 'SELL'
            confidence = min(0.9, base_confidence)
            metadata['signal_type'] = 'bearish_crossover'

        return Signal(
            timestamp=data.index[-1],
            action=action,
            price=current_price,
            confidence=confidence,
            metadata=metadata
        )

    def calculate_position_size(self, signal: Signal, account_balance: float) -> float:
        """Calculate position size based on confidence and risk management."""
        base_size = account_balance * self.max_position_size
        confidence_multiplier = signal.confidence
        return base_size * confidence_multiplier


class RSIMeanReversion(BaseStrategy):
    """
    RSI Mean Reversion strategy with dynamic thresholds.
    Optimized for high-frequency trading with robust entry/exit rules.
    """

    def __init__(
        self,
        symbol: str,
        timeframe: str,
        rsi_period: int = 14,
        oversold_threshold: float = 30,
        overbought_threshold: float = 70,
        trend_filter: bool = True,
        trend_period: int = 50
    ):
        parameters = {
            'rsi_period': rsi_period,
            'oversold_threshold': oversold_threshold,
            'overbought_threshold': overbought_threshold,
            'trend_filter': trend_filter,
            'trend_period': trend_period
        }

        super().__init__(
            name=f"RSI_MeanRev_{rsi_period}",
            symbol=symbol,
            timeframe=timeframe,
            parameters=parameters
        )

        self.rsi_period = rsi_period
        self.oversold_threshold = oversold_threshold
        self.overbought_threshold = overbought_threshold
        self.trend_filter = trend_filter
        self.trend_period = trend_period

    def generate_signal(self, data: pd.DataFrame) -> Signal:
        """Generate mean reversion signal based on RSI."""
        if len(data) < max(self.rsi_period, self.trend_period) + 1:
            return Signal(
                timestamp=data.index[-1],
                action='HOLD',
                price=data['close'].iloc[-1],
                confidence=0.0,
                metadata={'reason': 'insufficient_data'}
            )

        # Calculate RSI
        rsi = safe_rsi(data['close'].values, self.rsi_period)
        current_rsi = rsi[-1]

        # Trend filter
        if self.trend_filter:
            trend_ma = safe_sma(data['close'].values, self.trend_period)
            current_price = data['close'].iloc[-1]
            trend_direction = 1 if current_price > trend_ma[-1] else -1
        else:
            trend_direction = 0

        # Calculate volatility for adaptive thresholds
        close_prices = data['close'].values
        if len(close_prices) >= 20:
            price_window = close_prices[-20:]
            returns = np.diff(price_window) / price_window[:-1]
            volatility = np.std(returns) if len(returns) > 0 else 0.02
        else:
            volatility = 0.02

        # Adaptive RSI thresholds based on volatility
        vol_adjustment = volatility * 200  # Scale volatility
        adaptive_oversold = self.oversold_threshold + vol_adjustment
        adaptive_overbought = self.overbought_threshold - vol_adjustment

        # Ensure thresholds stay within reasonable bounds
        adaptive_oversold = min(45, max(20, adaptive_oversold))
        adaptive_overbought = max(55, min(80, adaptive_overbought))

        # Signal generation with improved logic
        action = 'HOLD'
        confidence = 0.0
        metadata = {
            'rsi': current_rsi,
            'trend_direction': trend_direction,
            'volatility': volatility,
            'adaptive_oversold': adaptive_oversold,
            'adaptive_overbought': adaptive_overbought
        }

        # Enhanced oversold condition (buy signal)
        if current_rsi < adaptive_oversold:
            base_confidence = 0.3

            # Boost confidence for extreme conditions
            extremity = (adaptive_oversold - current_rsi) / adaptive_oversold
            base_confidence += extremity * 0.4

            # Trend alignment bonus
            if not self.trend_filter or trend_direction >= 0:
                base_confidence += 0.2

            action = 'BUY'
            confidence = min(0.9, base_confidence)
            metadata['signal_type'] = 'oversold_buy'

        # Enhanced overbought condition (sell signal)
        elif current_rsi > adaptive_overbought:
            base_confidence = 0.3

            # Boost confidence for extreme conditions
            extremity = (current_rsi - adaptive_overbought) / (100 - adaptive_overbought)
            base_confidence += extremity * 0.4

            # Trend alignment bonus
            if not self.trend_filter or trend_direction <= 0:
                base_confidence += 0.2

            action = 'SELL'
            confidence = min(0.9, base_confidence)
            metadata['signal_type'] = 'overbought_sell'

        # Additional momentum signals for more trades
        elif 35 < current_rsi < 40 and trend_direction > 0:
            action = 'BUY'
            confidence = 0.3
            metadata['signal_type'] = 'momentum_buy'

        elif 60 < current_rsi < 65 and trend_direction < 0:
            action = 'SELL'
            confidence = 0.3
            metadata['signal_type'] = 'momentum_sell'

        return Signal(
            timestamp=data.index[-1],
            action=action,
            price=data['close'].iloc[-1],
            confidence=confidence,
            metadata=metadata
        )

    def calculate_position_size(self, signal: Signal, account_balance: float) -> float:
        """Calculate position size with RSI-based scaling."""
        base_size = account_balance * self.max_position_size

        # Scale based on RSI extremity
        rsi = signal.metadata.get('rsi', 50)
        if signal.action == 'BUY':
            extremity = max(0, (self.oversold_threshold - rsi) / self.oversold_threshold)
        elif signal.action == 'SELL':
            extremity = max(0, (rsi - self.overbought_threshold) / (100 - self.overbought_threshold))
        else:
            extremity = 0

        return base_size * (0.5 + 0.5 * extremity)


class BollingerBandsMomentum(BaseStrategy):
    """
    Bollinger Bands momentum strategy with volatility breakouts.
    Combines mean reversion and momentum for robust performance.
    """

    def __init__(
        self,
        symbol: str,
        timeframe: str,
        bb_period: int = 20,
        bb_std: float = 2.0,
        volume_threshold: float = 1.5,
        momentum_period: int = 10
    ):
        parameters = {
            'bb_period': bb_period,
            'bb_std': bb_std,
            'volume_threshold': volume_threshold,
            'momentum_period': momentum_period
        }

        super().__init__(
            name=f"BB_Momentum_{bb_period}_{bb_std}",
            symbol=symbol,
            timeframe=timeframe,
            parameters=parameters
        )

        self.bb_period = bb_period
        self.bb_std = bb_std
        self.volume_threshold = volume_threshold
        self.momentum_period = momentum_period

    def generate_signal(self, data: pd.DataFrame) -> Signal:
        """Generate signal based on Bollinger Bands breakout with volume confirmation."""
        if len(data) < max(self.bb_period, self.momentum_period) + 1:
            return Signal(
                timestamp=data.index[-1],
                action='HOLD',
                price=data['close'].iloc[-1],
                confidence=0.0,
                metadata={'reason': 'insufficient_data'}
            )

        # Calculate Bollinger Bands
        bb_upper, bb_middle, bb_lower = safe_bbands(
            data['close'].values,
            self.bb_period,
            self.bb_std
        )

        # Current values
        current_price = data['close'].iloc[-1]
        current_upper = bb_upper[-1]
        current_lower = bb_lower[-1]
        current_middle = bb_middle[-1]

        # Volume confirmation
        volume_ma = safe_sma(data['volume'].values, self.bb_period)
        current_volume = data['volume'].iloc[-1]
        volume_ratio = current_volume / volume_ma[-1]

        # Momentum (simple momentum calculation)
        momentum = np.diff(data['close'].values, n=self.momentum_period)
        momentum = np.concatenate([np.full(self.momentum_period, np.nan), momentum])
        current_momentum = momentum[-1]

        # Signal generation
        action = 'HOLD'
        confidence = 0.0
        metadata = {
            'bb_position': (current_price - current_lower) / (current_upper - current_lower),
            'volume_ratio': volume_ratio,
            'momentum': current_momentum
        }

        # Bullish breakout
        if (current_price > current_upper and
            volume_ratio > self.volume_threshold and
            current_momentum > 0):
            action = 'BUY'
            confidence = min(0.9, volume_ratio / self.volume_threshold * 0.5)
            metadata['signal_type'] = 'bullish_breakout'

        # Bearish breakout
        elif (current_price < current_lower and
              volume_ratio > self.volume_threshold and
              current_momentum < 0):
            action = 'SELL'
            confidence = min(0.9, volume_ratio / self.volume_threshold * 0.5)
            metadata['signal_type'] = 'bearish_breakout'

        # Mean reversion signals
        elif current_price < current_lower and current_momentum > 0:
            action = 'BUY'
            confidence = 0.3
            metadata['signal_type'] = 'mean_reversion_buy'

        elif current_price > current_upper and current_momentum < 0:
            action = 'SELL'
            confidence = 0.3
            metadata['signal_type'] = 'mean_reversion_sell'

        return Signal(
            timestamp=data.index[-1],
            action=action,
            price=current_price,
            confidence=confidence,
            metadata=metadata
        )

    def calculate_position_size(self, signal: Signal, account_balance: float) -> float:
        """Calculate position size based on signal strength and volatility."""
        base_size = account_balance * self.max_position_size

        # Adjust based on signal type and confidence
        signal_type = signal.metadata.get('signal_type', '')
        if 'breakout' in signal_type:
            # Larger positions for breakouts
            return base_size * signal.confidence * 1.2
        else:
            # Smaller positions for mean reversion
            return base_size * signal.confidence * 0.8


@jit(nopython=True)
def calculate_ema_fast(prices: np.ndarray, period: int) -> np.ndarray:
    """Fast EMA calculation using Numba JIT compilation."""
    alpha = 2.0 / (period + 1.0)
    ema = np.zeros(len(prices))
    ema[0] = prices[0]

    for i in range(1, len(prices)):
        ema[i] = alpha * prices[i] + (1 - alpha) * ema[i-1]

    return ema


class MACDStrategy(BaseStrategy):
    """
    MACD strategy with signal line crossovers and histogram analysis.
    Optimized with JIT compilation for maximum speed.
    """

    def __init__(
        self,
        symbol: str,
        timeframe: str,
        fast_period: int = 12,
        slow_period: int = 26,
        signal_period: int = 9,
        histogram_threshold: float = 0.0
    ):
        parameters = {
            'fast_period': fast_period,
            'slow_period': slow_period,
            'signal_period': signal_period,
            'histogram_threshold': histogram_threshold
        }

        super().__init__(
            name=f"MACD_{fast_period}_{slow_period}_{signal_period}",
            symbol=symbol,
            timeframe=timeframe,
            parameters=parameters
        )

        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period
        self.histogram_threshold = histogram_threshold

    def generate_signal(self, data: pd.DataFrame) -> Signal:
        """Generate MACD-based signals."""
        if len(data) < self.slow_period + self.signal_period:
            return Signal(
                timestamp=data.index[-1],
                action='HOLD',
                price=data['close'].iloc[-1],
                confidence=0.0,
                metadata={'reason': 'insufficient_data'}
            )

        # Calculate MACD
        macd, macd_signal, macd_histogram = safe_macd(
            data['close'].values,
            self.fast_period,
            self.slow_period,
            self.signal_period
        )

        # Current values
        current_macd = macd[-1]
        current_signal = macd_signal[-1]
        current_histogram = macd_histogram[-1]

        # Previous values for crossover detection
        prev_macd = macd[-2]
        prev_signal = macd_signal[-2]
        prev_histogram = macd_histogram[-2]

        # Signal generation
        action = 'HOLD'
        confidence = 0.0
        metadata = {
            'macd': current_macd,
            'signal': current_signal,
            'histogram': current_histogram
        }

        # Bullish signal line crossover
        if (prev_macd <= prev_signal and current_macd > current_signal and
            current_histogram > self.histogram_threshold):
            action = 'BUY'
            confidence = min(0.8, abs(current_histogram) * 100)
            metadata['signal_type'] = 'bullish_crossover'

        # Bearish signal line crossover
        elif (prev_macd >= prev_signal and current_macd < current_signal and
              current_histogram < -self.histogram_threshold):
            action = 'SELL'
            confidence = min(0.8, abs(current_histogram) * 100)
            metadata['signal_type'] = 'bearish_crossover'

        # Histogram momentum
        elif (current_histogram > prev_histogram and current_histogram > 0 and
              current_macd > current_signal):
            action = 'BUY'
            confidence = 0.4
            metadata['signal_type'] = 'histogram_momentum_bull'

        elif (current_histogram < prev_histogram and current_histogram < 0 and
              current_macd < current_signal):
            action = 'SELL'
            confidence = 0.4
            metadata['signal_type'] = 'histogram_momentum_bear'

        return Signal(
            timestamp=data.index[-1],
            action=action,
            price=data['close'].iloc[-1],
            confidence=confidence,
            metadata=metadata
        )

    def calculate_position_size(self, signal: Signal, account_balance: float) -> float:
        """Calculate position size based on MACD signal strength."""
        base_size = account_balance * self.max_position_size
        return base_size * signal.confidence


class AdvancedMomentumStrategy(BaseStrategy):
    """
    Advanced momentum strategy with multiple timeframe analysis.
    Optimized for high-frequency trading with adaptive parameters.
    """

    def __init__(
        self,
        symbol: str,
        timeframe: str,
        momentum_period: int = 14,
        roc_period: int = 10,
        volatility_period: int = 20,
        min_momentum_threshold: float = 0.01
    ):
        parameters = {
            'momentum_period': momentum_period,
            'roc_period': roc_period,
            'volatility_period': volatility_period,
            'min_momentum_threshold': min_momentum_threshold
        }

        super().__init__(
            name=f"AdvMomentum_{momentum_period}_{roc_period}",
            symbol=symbol,
            timeframe=timeframe,
            parameters=parameters
        )

        self.momentum_period = momentum_period
        self.roc_period = roc_period
        self.volatility_period = volatility_period
        self.min_momentum_threshold = min_momentum_threshold

    def generate_signal(self, data: pd.DataFrame) -> Signal:
        """Generate momentum-based signals with adaptive thresholds."""
        if len(data) < max(self.momentum_period, self.volatility_period) + 1:
            return Signal(
                timestamp=data.index[-1],
                action='HOLD',
                price=data['close'].iloc[-1],
                confidence=0.0,
                metadata={'reason': 'insufficient_data'}
            )

        # Calculate momentum indicators
        prices = data['close'].values
        current_price = prices[-1]

        # Rate of Change (ROC)
        roc = (current_price - prices[-self.roc_period]) / prices[-self.roc_period]

        # Price momentum
        momentum = (current_price - prices[-self.momentum_period]) / prices[-self.momentum_period]

        # Volatility-adjusted momentum
        if len(prices) >= self.volatility_period:
            price_window = prices[-self.volatility_period:]
            returns = np.diff(price_window) / price_window[:-1]
            volatility = np.std(returns) if len(returns) > 0 else 0.02
        else:
            volatility = 0.02

        # Adaptive threshold based on volatility
        adaptive_threshold = self.min_momentum_threshold * (1 + volatility * 10)

        # Volume momentum (if available)
        volume_momentum = 0
        if 'volume' in data.columns:
            current_volume = data['volume'].iloc[-1]
            avg_volume = np.mean(data['volume'].values[-self.momentum_period:])
            volume_momentum = (current_volume - avg_volume) / avg_volume

        # Signal generation
        action = 'HOLD'
        confidence = 0.0
        metadata = {
            'momentum': momentum,
            'roc': roc,
            'volatility': volatility,
            'volume_momentum': volume_momentum,
            'adaptive_threshold': adaptive_threshold
        }

        # Strong bullish momentum
        if momentum > adaptive_threshold and roc > 0:
            base_confidence = 0.4

            # Boost confidence with strong momentum
            momentum_strength = min(1.0, abs(momentum) / adaptive_threshold)
            base_confidence += momentum_strength * 0.3

            # Volume confirmation bonus
            if volume_momentum > 0.2:
                base_confidence += 0.2

            action = 'BUY'
            confidence = min(0.9, base_confidence)
            metadata['signal_type'] = 'strong_momentum_buy'

        # Strong bearish momentum
        elif momentum < -adaptive_threshold and roc < 0:
            base_confidence = 0.4

            # Boost confidence with strong momentum
            momentum_strength = min(1.0, abs(momentum) / adaptive_threshold)
            base_confidence += momentum_strength * 0.3

            # Volume confirmation bonus
            if volume_momentum > 0.2:
                base_confidence += 0.2

            action = 'SELL'
            confidence = min(0.9, base_confidence)
            metadata['signal_type'] = 'strong_momentum_sell'

        # Moderate momentum signals for more trades
        elif momentum > adaptive_threshold * 0.5 and roc > 0:
            action = 'BUY'
            confidence = 0.3
            metadata['signal_type'] = 'moderate_momentum_buy'

        elif momentum < -adaptive_threshold * 0.5 and roc < 0:
            action = 'SELL'
            confidence = 0.3
            metadata['signal_type'] = 'moderate_momentum_sell'

        return Signal(
            timestamp=data.index[-1],
            action=action,
            price=current_price,
            confidence=confidence,
            metadata=metadata
        )

    def calculate_position_size(self, signal: Signal, account_balance: float) -> float:
        """Calculate position size based on momentum strength."""
        base_size = account_balance * self.max_position_size

        # Scale based on momentum strength
        momentum = abs(signal.metadata.get('momentum', 0))
        momentum_multiplier = min(1.5, 1 + momentum * 10)

        return base_size * signal.confidence * momentum_multiplier
