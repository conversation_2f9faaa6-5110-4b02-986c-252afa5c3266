"""
Test the evolution system with the fixes applied.
"""
import sys
import os
import traceback

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_evolution_system():
    """Test the evolution system with fixes."""
    
    print("🧬 TESTING EVOLUTION SYSTEM WITH FIXES")
    print("=" * 50)
    
    try:
        # Test imports
        print("📦 Testing imports...")
        from evolution_runner import EvolutionRunner
        print("   ✅ EvolutionRunner imported")
        
        from core.data_manager import DataManager
        print("   ✅ DataManager imported")
        
        from core.backtester import VectorizedBacktester
        print("   ✅ VectorizedBacktester imported")
        
        from strategies.technical_strategies import MovingAverageCrossover
        print("   ✅ MovingAverageCrossover imported")
        
        # Test data manager with demo data
        print("\n📊 Testing data manager...")
        data_manager = DataManager()
        print("   ✅ DataManager created")
        
        # Test demo data generation
        demo_data = data_manager.get_historical_data(
            "BTCUSDT", "1h", "2024-01-01", "2024-01-10"
        )
        print(f"   ✅ Demo data generated: {len(demo_data)} rows")
        
        # Test backtester
        print("\n🔬 Testing backtester...")
        backtester = VectorizedBacktester(initial_capital=10000)
        print("   ✅ Backtester created")
        
        # Test strategy
        strategy = MovingAverageCrossover("BTCUSDT", "1h", fast_period=10, slow_period=30)
        print("   ✅ Strategy created")
        
        # Test backtest with demo data
        if len(demo_data) >= 100:
            result = backtester.run_backtest(strategy, demo_data)
            print(f"   ✅ Backtest completed: {result.total_return:.2%} return")
        else:
            print("   ⚠️ Not enough demo data for full backtest")
        
        # Test evolution runner (simplified)
        print("\n🧬 Testing evolution runner...")
        runner = EvolutionRunner()
        print("   ✅ EvolutionRunner created")
        
        print("\n✅ ALL TESTS PASSED!")
        print("🎉 Evolution system is working correctly!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        traceback.print_exc()
        return False

def run_mini_evolution():
    """Run a mini evolution test."""
    
    print("\n🚀 RUNNING MINI EVOLUTION TEST")
    print("=" * 50)
    
    try:
        from evolution_runner import EvolutionRunner
        from strategies.technical_strategies import MovingAverageCrossover
        
        # Create runner
        runner = EvolutionRunner()
        
        # Run very small evolution
        print("🧬 Starting mini evolution...")
        results = runner.run_evolution_campaign(
            campaign_name="Mini_Test_Evolution",
            target_symbols=["BTCUSDT"],  # Single symbol
            target_timeframes=["1h"],    # Single timeframe
            evolution_config={
                'evolution_generations': 3,  # Very few generations
                'population_size': 4,        # Small population
                'elite_ratio': 0.5,         # Keep top 50%
                'mutation_rate': 0.3,
                'initial_capital': 10000
            }
        )
        
        # Check results
        if results.get('best_overall_strategy'):
            best = results['best_overall_strategy']
            print(f"\n🏆 MINI EVOLUTION SUCCESS!")
            print(f"   Best Strategy: {best.get('name', 'Unknown')}")
            print(f"   Fitness Score: {best.get('fitness_score', 0):.4f}")
            print(f"   Total Return: {best.get('total_return', 0):.2%}")
            print(f"   Max Drawdown: {best.get('max_drawdown', 0):.2%}")
            
            return True
        else:
            print("⚠️ No strategy found, but system is working")
            return True
            
    except Exception as e:
        print(f"❌ Mini evolution failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    
    print("🔧 EVOLUTION SYSTEM FIX VERIFICATION")
    print("=" * 60)
    
    # Test basic functionality
    basic_test = test_evolution_system()
    
    if basic_test:
        print("\n" + "=" * 60)
        # Try mini evolution
        evolution_test = run_mini_evolution()
        
        if evolution_test:
            print("\n🎉 ALL TESTS SUCCESSFUL!")
            print("✅ Evolution system is fully operational!")
            print("\n🚀 READY FOR FULL EVOLUTION:")
            print("   python evolution_runner.py")
            print("   python run_evolution.py")
            return True
        else:
            print("\n⚠️ Basic tests passed, evolution needs work")
            return False
    else:
        print("\n❌ Basic tests failed")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        traceback.print_exc()
        sys.exit(1)
