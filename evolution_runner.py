"""
Evolution Runner - Advanced iterative strategy evolution with robust validation.
Generates world-class trading strategies through evolutionary optimization.
"""
import sys
import os
import time
from datetime import datetime, timedelta
import traceback

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from loguru import logger
from core.evolution_engine import EvolutionEngine
from core.data_manager import DataManager
from strategies.technical_strategies import (
    MovingAverageCrossover, RSIMeanReversion, 
    BollingerBandsMomentum, MACDStrategy
)


class EvolutionRunner:
    """
    Main evolution runner for generating world-class trading strategies.
    Implements comprehensive validation and real-world robustness testing.
    """
    
    def __init__(self):
        self.data_manager = DataManager()
        self.evolution_engine = None
        
        # Setup advanced logging
        self._setup_logging()
        
        logger.info("🧬 Evolution Runner initialized")
    
    def _setup_logging(self):
        """Setup comprehensive logging for evolution process."""
        # Remove default logger
        logger.remove()
        
        # Console logger with colors
        logger.add(
            sys.stdout,
            level="INFO",
            format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{message}</cyan>",
            colorize=True
        )
        
        # File logger for detailed tracking
        log_file = f"logs/evolution_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        os.makedirs("logs", exist_ok=True)
        
        logger.add(
            log_file,
            level="DEBUG",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {message}",
            rotation="50 MB",
            retention="30 days"
        )
        
        logger.info(f"📝 Detailed logging to: {log_file}")
    
    def run_evolution_campaign(
        self,
        campaign_name: str = "WorldClass_Strategy_Evolution",
        target_symbols: list = None,
        target_timeframes: list = None,
        evolution_config: dict = None
    ) -> dict:
        """
        Run comprehensive evolution campaign to generate world-class strategies.
        
        Args:
            campaign_name: Name for this evolution campaign
            target_symbols: List of symbols to evolve strategies for
            target_timeframes: List of timeframes to test
            evolution_config: Evolution configuration parameters
            
        Returns:
            Comprehensive evolution results with validated strategies
        """
        
        logger.info(f"🚀 STARTING EVOLUTION CAMPAIGN: {campaign_name}")
        logger.info("=" * 80)
        
        # Default configuration for world-class results
        if target_symbols is None:
            target_symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT"]
        
        if target_timeframes is None:
            target_timeframes = ["1h", "4h"]
        
        if evolution_config is None:
            evolution_config = {
                'evolution_generations': 30,
                'population_size': 24,
                'elite_ratio': 0.25,
                'mutation_rate': 0.3,
                'initial_capital': 100000
            }
        
        logger.info(f"🎯 Target Symbols: {target_symbols}")
        logger.info(f"⏰ Target Timeframes: {target_timeframes}")
        logger.info(f"🧬 Evolution Config: {evolution_config}")
        
        campaign_start_time = time.time()
        campaign_results = {
            'campaign_name': campaign_name,
            'start_time': datetime.now(),
            'symbols_tested': target_symbols,
            'timeframes_tested': target_timeframes,
            'evolution_config': evolution_config,
            'symbol_results': {},
            'best_overall_strategy': None,
            'campaign_statistics': {},
            'validation_summary': {},
            'production_recommendations': {}
        }
        
        # Strategy classes to evolve
        strategy_classes = [
            MovingAverageCrossover,
            RSIMeanReversion,
            BollingerBandsMomentum,
            MACDStrategy
        ]
        
        logger.info(f"🎯 Strategy Classes: {[cls.__name__ for cls in strategy_classes]}")
        
        # Run evolution for each symbol
        all_evolved_strategies = []
        
        for symbol in target_symbols:
            logger.info(f"\n{'='*60}")
            logger.info(f"🔬 EVOLVING STRATEGIES FOR {symbol}")
            logger.info(f"{'='*60}")
            
            try:
                symbol_results = self._evolve_symbol_strategies(
                    symbol=symbol,
                    timeframes=target_timeframes,
                    strategy_classes=strategy_classes,
                    evolution_config=evolution_config
                )
                
                campaign_results['symbol_results'][symbol] = symbol_results
                
                # Collect all strategies for global comparison
                for tf_result in symbol_results.values():
                    if 'best_strategy' in tf_result:
                        all_evolved_strategies.append(tf_result['best_strategy'])
                
                logger.info(f"✅ Completed evolution for {symbol}")
                
            except Exception as e:
                logger.error(f"❌ Evolution failed for {symbol}: {e}")
                logger.debug(traceback.format_exc())
                continue
        
        # Find best overall strategy across all symbols/timeframes
        if all_evolved_strategies:
            campaign_results['best_overall_strategy'] = self._find_global_champion(
                all_evolved_strategies
            )
            
            # Generate comprehensive validation
            campaign_results['validation_summary'] = self._validate_campaign_results(
                campaign_results
            )
            
            # Generate production recommendations
            campaign_results['production_recommendations'] = self._generate_production_recommendations(
                campaign_results
            )
        
        # Calculate campaign statistics
        campaign_time = time.time() - campaign_start_time
        campaign_results['campaign_statistics'] = self._calculate_campaign_statistics(
            campaign_results, campaign_time
        )
        
        # Save comprehensive results
        self._save_campaign_results(campaign_results)
        
        # Generate final report
        self._generate_campaign_report(campaign_results)
        
        logger.info(f"\n🏁 EVOLUTION CAMPAIGN COMPLETED")
        logger.info(f"⏱️ Total Time: {campaign_time/60:.1f} minutes")
        
        return campaign_results
    
    def _evolve_symbol_strategies(
        self,
        symbol: str,
        timeframes: list,
        strategy_classes: list,
        evolution_config: dict
    ) -> dict:
        """Evolve strategies for a specific symbol across timeframes."""
        
        symbol_results = {}
        
        for timeframe in timeframes:
            logger.info(f"\n🔄 Evolving {symbol} - {timeframe}")
            logger.info("-" * 40)
            
            try:
                # Initialize evolution engine for this symbol/timeframe
                self.evolution_engine = EvolutionEngine(
                    data_manager=self.data_manager,
                    initial_capital=evolution_config['initial_capital'],
                    evolution_generations=evolution_config['evolution_generations'],
                    population_size=evolution_config['population_size'],
                    elite_ratio=evolution_config['elite_ratio'],
                    mutation_rate=evolution_config['mutation_rate']
                )
                
                # Define data period for evolution
                end_date = datetime.now().strftime('%Y-%m-%d')
                start_date = (datetime.now() - timedelta(days=730)).strftime('%Y-%m-%d')  # 2 years
                
                logger.info(f"📊 Data period: {start_date} to {end_date}")
                
                # Run evolution
                evolution_results = self.evolution_engine.evolve_strategies(
                    strategy_classes=strategy_classes,
                    symbols=[symbol],
                    timeframes=[timeframe],
                    start_date=start_date,
                    end_date=end_date
                )
                
                symbol_results[timeframe] = evolution_results
                
                # Log key results
                if 'best_strategy' in evolution_results:
                    best = evolution_results['best_strategy']
                    logger.info(f"🏆 Best Strategy: {best.get('name', 'Unknown')}")
                    logger.info(f"📊 Fitness Score: {best.get('fitness_score', 0):.4f}")
                    logger.info(f"🛡️ Robustness: {best.get('robustness_score', 0):.4f}")
                    logger.info(f"📈 Return: {best.get('total_return', 0):.2%}")
                    logger.info(f"📉 Max DD: {best.get('max_drawdown', 0):.2%}")
                
            except Exception as e:
                logger.error(f"❌ Evolution failed for {symbol}-{timeframe}: {e}")
                logger.debug(traceback.format_exc())
                symbol_results[timeframe] = {'error': str(e)}
        
        return symbol_results
    
    def _find_global_champion(self, all_strategies: list) -> dict:
        """Find the best strategy across all symbols and timeframes."""
        
        logger.info(f"\n🏆 FINDING GLOBAL CHAMPION from {len(all_strategies)} strategies")
        
        if not all_strategies:
            return {}
        
        # Score each strategy using comprehensive criteria
        scored_strategies = []
        
        for strategy in all_strategies:
            # Comprehensive scoring formula
            fitness_score = strategy.get('fitness_score', 0)
            robustness_score = strategy.get('robustness_score', 0)
            total_return = strategy.get('total_return', 0)
            max_drawdown = strategy.get('max_drawdown', 1)
            sharpe_ratio = strategy.get('sharpe_ratio', 0)
            
            # Global champion score (weighted combination)
            global_score = (
                fitness_score * 0.3 +           # Overall fitness (30%)
                robustness_score * 0.3 +        # Robustness validation (30%)
                (sharpe_ratio / 3) * 0.2 +      # Risk-adjusted returns (20%)
                (1 - max_drawdown) * 0.1 +      # Drawdown control (10%)
                (total_return / 2) * 0.1        # Total returns (10%)
            )
            
            scored_strategies.append({
                'strategy': strategy,
                'global_score': global_score
            })
        
        # Find champion
        champion = max(scored_strategies, key=lambda x: x['global_score'])
        
        logger.info(f"🥇 GLOBAL CHAMPION FOUND!")
        logger.info(f"   Strategy: {champion['strategy'].get('name', 'Unknown')}")
        logger.info(f"   Global Score: {champion['global_score']:.4f}")
        logger.info(f"   Symbol: {champion['strategy'].get('symbol', 'Unknown')}")
        logger.info(f"   Timeframe: {champion['strategy'].get('timeframe', 'Unknown')}")
        
        return champion['strategy']
    
    def _validate_campaign_results(self, campaign_results: dict) -> dict:
        """Comprehensive validation of campaign results."""
        
        logger.info(f"\n🔍 VALIDATING CAMPAIGN RESULTS")
        
        validation_summary = {
            'total_strategies_evolved': 0,
            'successful_evolutions': 0,
            'failed_evolutions': 0,
            'strategies_meeting_criteria': 0,
            'production_ready_strategies': 0,
            'average_fitness_score': 0,
            'average_robustness_score': 0,
            'validation_grade': 'F'
        }
        
        all_strategies = []
        successful_count = 0
        
        # Collect all strategies
        for symbol, symbol_results in campaign_results['symbol_results'].items():
            for timeframe, tf_results in symbol_results.items():
                validation_summary['total_strategies_evolved'] += 1
                
                if 'error' not in tf_results and 'best_strategy' in tf_results:
                    successful_count += 1
                    strategy = tf_results['best_strategy']
                    all_strategies.append(strategy)
                    
                    # Check if meets production criteria
                    if self._meets_production_criteria(strategy):
                        validation_summary['strategies_meeting_criteria'] += 1
                    
                    # Check production readiness
                    production_assessment = tf_results.get('production_ready', {})
                    if production_assessment.get('ready', False):
                        validation_summary['production_ready_strategies'] += 1
                else:
                    validation_summary['failed_evolutions'] += 1
        
        validation_summary['successful_evolutions'] = successful_count
        
        # Calculate averages
        if all_strategies:
            fitness_scores = [s.get('fitness_score', 0) for s in all_strategies]
            robustness_scores = [s.get('robustness_score', 0) for s in all_strategies]
            
            validation_summary['average_fitness_score'] = sum(fitness_scores) / len(fitness_scores)
            validation_summary['average_robustness_score'] = sum(robustness_scores) / len(robustness_scores)
        
        # Assign validation grade
        success_rate = successful_count / max(1, validation_summary['total_strategies_evolved'])
        avg_fitness = validation_summary['average_fitness_score']
        
        if success_rate >= 0.8 and avg_fitness >= 0.7:
            validation_summary['validation_grade'] = 'A+'
        elif success_rate >= 0.7 and avg_fitness >= 0.6:
            validation_summary['validation_grade'] = 'A'
        elif success_rate >= 0.6 and avg_fitness >= 0.5:
            validation_summary['validation_grade'] = 'B'
        elif success_rate >= 0.5 and avg_fitness >= 0.4:
            validation_summary['validation_grade'] = 'C'
        else:
            validation_summary['validation_grade'] = 'D'
        
        logger.info(f"📊 Validation Grade: {validation_summary['validation_grade']}")
        logger.info(f"✅ Success Rate: {success_rate:.1%}")
        logger.info(f"📈 Avg Fitness: {validation_summary['average_fitness_score']:.4f}")
        logger.info(f"🛡️ Avg Robustness: {validation_summary['average_robustness_score']:.4f}")
        
        return validation_summary
    
    def _meets_production_criteria(self, strategy: dict) -> bool:
        """Check if strategy meets production criteria."""
        criteria = {
            'min_fitness': 0.6,
            'min_robustness': 0.6,
            'min_sharpe': 0.5,
            'max_drawdown': 0.25,
            'min_trades': 30
        }
        
        return (
            strategy.get('fitness_score', 0) >= criteria['min_fitness'] and
            strategy.get('robustness_score', 0) >= criteria['min_robustness'] and
            strategy.get('sharpe_ratio', 0) >= criteria['min_sharpe'] and
            strategy.get('max_drawdown', 1) <= criteria['max_drawdown'] and
            strategy.get('total_trades', 0) >= criteria['min_trades']
        )
    
    def _generate_production_recommendations(self, campaign_results: dict) -> dict:
        """Generate production deployment recommendations."""
        
        recommendations = {
            'immediate_deployment': [],
            'needs_improvement': [],
            'not_recommended': [],
            'overall_recommendation': '',
            'risk_assessment': '',
            'deployment_strategy': ''
        }
        
        best_strategy = campaign_results.get('best_overall_strategy', {})
        
        if best_strategy:
            fitness = best_strategy.get('fitness_score', 0)
            robustness = best_strategy.get('robustness_score', 0)
            
            if fitness >= 0.8 and robustness >= 0.8:
                recommendations['immediate_deployment'].append(best_strategy)
                recommendations['overall_recommendation'] = "IMMEDIATE DEPLOYMENT RECOMMENDED"
                recommendations['risk_assessment'] = "LOW RISK - Excellent performance and robustness"
                recommendations['deployment_strategy'] = "Deploy with full capital allocation"
            elif fitness >= 0.7 and robustness >= 0.7:
                recommendations['immediate_deployment'].append(best_strategy)
                recommendations['overall_recommendation'] = "DEPLOYMENT RECOMMENDED"
                recommendations['risk_assessment'] = "MODERATE RISK - Good performance and robustness"
                recommendations['deployment_strategy'] = "Deploy with conservative capital allocation"
            elif fitness >= 0.6 and robustness >= 0.6:
                recommendations['needs_improvement'].append(best_strategy)
                recommendations['overall_recommendation'] = "NEEDS IMPROVEMENT"
                recommendations['risk_assessment'] = "HIGH RISK - Requires further optimization"
                recommendations['deployment_strategy'] = "Paper trading recommended first"
            else:
                recommendations['not_recommended'].append(best_strategy)
                recommendations['overall_recommendation'] = "NOT RECOMMENDED"
                recommendations['risk_assessment'] = "VERY HIGH RISK - Insufficient performance"
                recommendations['deployment_strategy'] = "Continue evolution process"
        
        return recommendations
    
    def _calculate_campaign_statistics(self, campaign_results: dict, campaign_time: float) -> dict:
        """Calculate comprehensive campaign statistics."""
        
        stats = {
            'total_execution_time_minutes': campaign_time / 60,
            'symbols_processed': len(campaign_results['symbols_tested']),
            'timeframes_processed': len(campaign_results['timeframes_tested']),
            'total_combinations': len(campaign_results['symbols_tested']) * len(campaign_results['timeframes_tested']),
            'successful_combinations': 0,
            'average_time_per_combination': 0,
            'best_fitness_achieved': 0,
            'best_robustness_achieved': 0,
            'evolution_efficiency': 0
        }
        
        # Count successful combinations
        for symbol_results in campaign_results['symbol_results'].values():
            for tf_results in symbol_results.values():
                if 'error' not in tf_results and 'best_strategy' in tf_results:
                    stats['successful_combinations'] += 1
                    
                    strategy = tf_results['best_strategy']
                    stats['best_fitness_achieved'] = max(
                        stats['best_fitness_achieved'],
                        strategy.get('fitness_score', 0)
                    )
                    stats['best_robustness_achieved'] = max(
                        stats['best_robustness_achieved'],
                        strategy.get('robustness_score', 0)
                    )
        
        # Calculate efficiency metrics
        if stats['total_combinations'] > 0:
            stats['average_time_per_combination'] = campaign_time / stats['total_combinations']
            stats['evolution_efficiency'] = stats['successful_combinations'] / stats['total_combinations']
        
        return stats
    
    def _save_campaign_results(self, campaign_results: dict):
        """Save comprehensive campaign results."""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"evolution_campaign_{campaign_results['campaign_name']}_{timestamp}.json"
            
            # Make results JSON serializable
            serializable_results = self._make_json_serializable(campaign_results)
            
            import json
            with open(filename, 'w') as f:
                json.dump(serializable_results, f, indent=2, default=str)
            
            logger.info(f"💾 Campaign results saved to: {filename}")
            
        except Exception as e:
            logger.error(f"Failed to save campaign results: {e}")
    
    def _make_json_serializable(self, obj):
        """Convert object to JSON serializable format."""
        if isinstance(obj, dict):
            return {k: self._make_json_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        elif isinstance(obj, (datetime,)):
            return obj.isoformat()
        elif isinstance(obj, type):
            return obj.__name__
        elif hasattr(obj, '__dict__'):
            return self._make_json_serializable(obj.__dict__)
        else:
            return obj
    
    def _generate_campaign_report(self, campaign_results: dict):
        """Generate comprehensive campaign report."""
        
        logger.info(f"\n{'='*80}")
        logger.info("🏆 EVOLUTION CAMPAIGN FINAL REPORT")
        logger.info(f"{'='*80}")
        
        # Campaign overview
        logger.info(f"Campaign: {campaign_results['campaign_name']}")
        logger.info(f"Duration: {campaign_results['campaign_statistics']['total_execution_time_minutes']:.1f} minutes")
        logger.info(f"Symbols: {campaign_results['symbols_tested']}")
        logger.info(f"Timeframes: {campaign_results['timeframes_tested']}")
        
        # Validation summary
        validation = campaign_results['validation_summary']
        logger.info(f"\n📊 VALIDATION SUMMARY")
        logger.info(f"   Grade: {validation['validation_grade']}")
        logger.info(f"   Success Rate: {validation['successful_evolutions']}/{validation['total_strategies_evolved']}")
        logger.info(f"   Avg Fitness: {validation['average_fitness_score']:.4f}")
        logger.info(f"   Avg Robustness: {validation['average_robustness_score']:.4f}")
        logger.info(f"   Production Ready: {validation['production_ready_strategies']}")
        
        # Best strategy
        best = campaign_results.get('best_overall_strategy', {})
        if best:
            logger.info(f"\n🥇 GLOBAL CHAMPION")
            logger.info(f"   Strategy: {best.get('name', 'Unknown')}")
            logger.info(f"   Class: {best.get('class', {}).get('__name__', 'Unknown')}")
            logger.info(f"   Symbol: {best.get('symbol', 'Unknown')}")
            logger.info(f"   Timeframe: {best.get('timeframe', 'Unknown')}")
            logger.info(f"   Fitness: {best.get('fitness_score', 0):.4f}")
            logger.info(f"   Robustness: {best.get('robustness_score', 0):.4f}")
            logger.info(f"   Return: {best.get('total_return', 0):.2%}")
            logger.info(f"   Sharpe: {best.get('sharpe_ratio', 0):.4f}")
            logger.info(f"   Max DD: {best.get('max_drawdown', 0):.2%}")
            logger.info(f"   Trades: {best.get('total_trades', 0)}")
        
        # Production recommendation
        recommendation = campaign_results['production_recommendations']
        logger.info(f"\n🚀 PRODUCTION RECOMMENDATION")
        logger.info(f"   Status: {recommendation['overall_recommendation']}")
        logger.info(f"   Risk: {recommendation['risk_assessment']}")
        logger.info(f"   Strategy: {recommendation['deployment_strategy']}")
        
        logger.info(f"\n{'='*80}")


def main():
    """Main execution function for evolution runner."""
    
    print("🧬 CRYPTOCURRENCY STRATEGY EVOLUTION SYSTEM")
    print("=" * 60)
    print("Advanced iterative evolution with robust validation")
    print("Generating world-class trading strategies...")
    
    try:
        # Initialize evolution runner
        runner = EvolutionRunner()
        
        # Run comprehensive evolution campaign
        results = runner.run_evolution_campaign(
            campaign_name="WorldClass_Crypto_Strategies_v1",
            target_symbols=["BTCUSDT", "ETHUSDT"],  # Start with major pairs
            target_timeframes=["1h", "4h"],         # Test multiple timeframes
            evolution_config={
                'evolution_generations': 25,        # Sufficient for convergence
                'population_size': 20,              # Good diversity
                'elite_ratio': 0.25,               # Keep top 25%
                'mutation_rate': 0.3,              # Moderate mutation
                'initial_capital': 100000          # $100k starting capital
            }
        )
        
        # Check if we found a world-class strategy
        if results.get('best_overall_strategy'):
            best = results['best_overall_strategy']
            fitness = best.get('fitness_score', 0)
            robustness = best.get('robustness_score', 0)
            
            if fitness >= 0.8 and robustness >= 0.8:
                print("\n🎉 WORLD-CLASS STRATEGY DISCOVERED!")
                print(f"   Fitness Score: {fitness:.4f}")
                print(f"   Robustness Score: {robustness:.4f}")
                print(f"   Ready for production deployment!")
            elif fitness >= 0.7 and robustness >= 0.7:
                print("\n✅ HIGH-QUALITY STRATEGY FOUND!")
                print(f"   Fitness Score: {fitness:.4f}")
                print(f"   Robustness Score: {robustness:.4f}")
                print(f"   Suitable for careful deployment")
            else:
                print("\n⚠️ Strategy needs further evolution")
                print(f"   Current Fitness: {fitness:.4f}")
                print(f"   Current Robustness: {robustness:.4f}")
        
        return True
        
    except Exception as e:
        logger.error(f"Evolution campaign failed: {e}")
        logger.debug(traceback.format_exc())
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
