"""
Base strategy class with optimized performance tracking and execution.
All trading strategies inherit from this class.
"""
import time
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from numba import jit
from loguru import logger


@dataclass
class Signal:
    """Trading signal with timestamp and metadata."""
    timestamp: pd.Timestamp
    action: str  # 'BUY', 'SELL', 'HOLD'
    price: float
    confidence: float
    metadata: Dict[str, Any]


@dataclass
class Position:
    """Trading position representation."""
    symbol: str
    side: str  # 'LONG', 'SHORT'
    entry_price: float
    quantity: float
    entry_time: pd.Timestamp
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    current_price: Optional[float] = None
    
    @property
    def unrealized_pnl(self) -> float:
        """Calculate unrealized P&L."""
        if self.current_price is None:
            return 0.0
        
        if self.side == 'LONG':
            return (self.current_price - self.entry_price) * self.quantity
        else:
            return (self.entry_price - self.current_price) * self.quantity
    
    @property
    def unrealized_pnl_percentage(self) -> float:
        """Calculate unrealized P&L percentage."""
        if self.current_price is None:
            return 0.0
        
        if self.side == 'LONG':
            return ((self.current_price - self.entry_price) / self.entry_price) * 100
        else:
            return ((self.entry_price - self.current_price) / self.entry_price) * 100


class BaseStrategy(ABC):
    """
    Base class for all trading strategies.
    Provides common functionality and enforces strategy interface.
    """
    
    def __init__(
        self,
        name: str,
        symbol: str,
        timeframe: str,
        parameters: Dict[str, Any] = None
    ):
        self.name = name
        self.symbol = symbol
        self.timeframe = timeframe
        self.parameters = parameters or {}
        
        # Performance tracking
        self.trades = []
        self.positions = []
        self.signals = []
        self.execution_times = []
        
        # Strategy state
        self.is_initialized = False
        self.last_signal_time = None
        
        # Risk management
        self.max_position_size = 0.1
        self.stop_loss_pct = 0.02
        self.take_profit_pct = 0.06
        
        logger.info(f"Initialized strategy: {self.name} for {self.symbol}")
    
    @abstractmethod
    def generate_signal(self, data: pd.DataFrame) -> Signal:
        """
        Generate trading signal based on market data.
        
        Args:
            data: OHLCV DataFrame with technical indicators
            
        Returns:
            Signal object with action and metadata
        """
        pass
    
    @abstractmethod
    def calculate_position_size(self, signal: Signal, account_balance: float) -> float:
        """
        Calculate position size based on signal and risk management.
        
        Args:
            signal: Trading signal
            account_balance: Current account balance
            
        Returns:
            Position size in base currency
        """
        pass
    
    def initialize(self, data: pd.DataFrame) -> None:
        """Initialize strategy with historical data."""
        if len(data) < 50:  # Minimum data requirement
            raise ValueError("Insufficient data for strategy initialization")
        
        self.is_initialized = True
        logger.info(f"Strategy {self.name} initialized with {len(data)} data points")
    
    def process_data(self, data: pd.DataFrame) -> Signal:
        """
        Process market data and generate signal with performance tracking.
        
        Args:
            data: Market data DataFrame
            
        Returns:
            Trading signal
        """
        start_time = time.perf_counter()
        
        if not self.is_initialized:
            self.initialize(data)
        
        # Generate signal
        signal = self.generate_signal(data)
        
        # Track execution time
        execution_time = time.perf_counter() - start_time
        self.execution_times.append(execution_time)
        
        # Store signal
        self.signals.append(signal)
        self.last_signal_time = signal.timestamp
        
        return signal
    
    def execute_trade(
        self, 
        signal: Signal, 
        current_price: float, 
        account_balance: float
    ) -> Optional[Position]:
        """
        Execute trade based on signal.
        
        Args:
            signal: Trading signal
            current_price: Current market price
            account_balance: Available balance
            
        Returns:
            Position object if trade executed, None otherwise
        """
        if signal.action == 'HOLD':
            return None
        
        # Calculate position size
        position_size = self.calculate_position_size(signal, account_balance)
        
        if position_size <= 0:
            return None
        
        # Create position
        position = Position(
            symbol=self.symbol,
            side='LONG' if signal.action == 'BUY' else 'SHORT',
            entry_price=current_price,
            quantity=position_size,
            entry_time=signal.timestamp,
            stop_loss=self._calculate_stop_loss(current_price, signal.action),
            take_profit=self._calculate_take_profit(current_price, signal.action)
        )
        
        self.positions.append(position)
        logger.info(f"Executed {signal.action} trade: {position}")
        
        return position
    
    def _calculate_stop_loss(self, price: float, action: str) -> float:
        """Calculate stop loss price."""
        if action == 'BUY':
            return price * (1 - self.stop_loss_pct)
        else:
            return price * (1 + self.stop_loss_pct)
    
    def _calculate_take_profit(self, price: float, action: str) -> float:
        """Calculate take profit price."""
        if action == 'BUY':
            return price * (1 + self.take_profit_pct)
        else:
            return price * (1 - self.take_profit_pct)
    
    def update_positions(self, current_price: float) -> List[Position]:
        """Update all open positions with current price."""
        closed_positions = []
        
        for position in self.positions:
            position.current_price = current_price
            
            # Check stop loss and take profit
            if self._should_close_position(position):
                closed_positions.append(position)
                self.positions.remove(position)
                self.trades.append(self._create_trade_record(position))
        
        return closed_positions
    
    def _should_close_position(self, position: Position) -> bool:
        """Check if position should be closed."""
        if position.current_price is None:
            return False
        
        if position.side == 'LONG':
            return (
                position.current_price <= position.stop_loss or
                position.current_price >= position.take_profit
            )
        else:
            return (
                position.current_price >= position.stop_loss or
                position.current_price <= position.take_profit
            )
    
    def _create_trade_record(self, position: Position) -> Dict[str, Any]:
        """Create trade record for closed position."""
        return {
            'symbol': position.symbol,
            'side': position.side,
            'entry_price': position.entry_price,
            'exit_price': position.current_price,
            'quantity': position.quantity,
            'entry_time': position.entry_time,
            'exit_time': pd.Timestamp.now(),
            'pnl': position.unrealized_pnl,
            'pnl_percentage': position.unrealized_pnl_percentage,
            'duration': pd.Timestamp.now() - position.entry_time
        }
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """Calculate comprehensive performance metrics."""
        if not self.trades:
            return {}
        
        trades_df = pd.DataFrame(self.trades)
        
        # Basic metrics
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['pnl'] > 0])
        losing_trades = len(trades_df[trades_df['pnl'] < 0])
        
        # Performance calculations
        total_return = trades_df['pnl'].sum()
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        avg_win = trades_df[trades_df['pnl'] > 0]['pnl'].mean() if winning_trades > 0 else 0
        avg_loss = trades_df[trades_df['pnl'] < 0]['pnl'].mean() if losing_trades > 0 else 0
        profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades)) if avg_loss != 0 else 0
        
        # Execution performance
        avg_execution_time = np.mean(self.execution_times) if self.execution_times else 0
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'total_return': total_return,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'avg_execution_time': avg_execution_time,
            'max_execution_time': max(self.execution_times) if self.execution_times else 0,
            'min_execution_time': min(self.execution_times) if self.execution_times else 0
        }
    
    def reset(self):
        """Reset strategy state for new backtest."""
        self.trades = []
        self.positions = []
        self.signals = []
        self.execution_times = []
        self.is_initialized = False
        self.last_signal_time = None
    
    def __str__(self) -> str:
        return f"Strategy({self.name}, {self.symbol}, {self.timeframe})"
    
    def __repr__(self) -> str:
        return self.__str__()
