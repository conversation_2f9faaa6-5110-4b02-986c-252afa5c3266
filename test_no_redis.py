"""
Test to verify Red<PERSON> is completely removed and system works with API only.
"""
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_no_redis():
    """Test that system works without Redis."""
    
    print("🔍 TESTING NO REDIS DEPENDENCY")
    print("=" * 50)
    
    try:
        # Test data manager import
        print("📊 Testing DataManager import...")
        from core.data_manager import DataManager
        print("   ✅ DataManager imported successfully")
        
        # Create data manager
        print("   🔧 Creating DataManager instance...")
        data_manager = DataManager()
        print("   ✅ DataManager created without Redis errors")
        
        # Test demo data generation
        print("   📈 Testing demo data generation...")
        demo_data = data_manager._generate_demo_data(
            "BTCUSDT", "1h", "2024-01-01", "2024-01-02"
        )
        
        if not demo_data.empty:
            print(f"   ✅ Demo data generated: {len(demo_data)} rows")
            print(f"   📊 Price range: ${demo_data['close'].min():.2f} - ${demo_data['close'].max():.2f}")
        else:
            print("   ❌ Demo data generation failed")
            return False
        
        # Test strategy
        print("\n🎯 Testing strategy...")
        from strategies.technical_strategies import MovingAverageCrossover
        strategy = MovingAverageCrossover("BTCUSDT", "1h", fast_period=10, slow_period=30)
        print("   ✅ Strategy created")
        
        # Test backtester
        print("\n🔬 Testing backtester...")
        from core.backtester import VectorizedBacktester
        backtester = VectorizedBacktester(initial_capital=10000)
        print("   ✅ Backtester created")
        
        # Run backtest with demo data
        if len(demo_data) >= 50:
            result = backtester.run_backtest(strategy, demo_data)
            print("   ✅ Backtest completed")
            print(f"   📊 Total Return: {result.total_return:.2%}")
            print(f"   📊 Total Trades: {result.total_trades}")
        else:
            print("   ⚠️ Insufficient demo data")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_settings():
    """Test settings without Redis."""
    
    print("\n⚙️ TESTING SETTINGS")
    print("=" * 50)
    
    try:
        from config.settings import Settings
        settings = Settings()
        
        print(f"   ✅ Cache enabled: {settings.cache_enabled}")
        print(f"   ✅ Parallel processing: {settings.parallel_processing}")
        print(f"   ✅ Default symbol: {settings.default_symbol}")
        
        # Verify Redis is disabled
        if not settings.cache_enabled:
            print("   ✅ Redis/Cache is properly disabled")
            return True
        else:
            print("   ⚠️ Cache is still enabled")
            return False
            
    except Exception as e:
        print(f"❌ Settings test failed: {e}")
        return False

def main():
    """Main test function."""
    
    print("🚀 NO REDIS DEPENDENCY TEST")
    print("=" * 60)
    print("Verifying system works without Redis/localhost")
    print()
    
    tests = [
        ("No Redis Dependency", test_no_redis),
        ("Settings Configuration", test_settings)
    ]
    
    passed = 0
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n📊 RESULTS: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Redis dependency completely removed!")
        print("✅ System works with API-only configuration!")
        print("✅ No localhost connections required!")
        
        print("\n🚀 READY TO USE:")
        print("   1. Configure API: python setup_binance.py")
        print("   2. Test API: python test_binance_api.py")
        print("   3. Run framework: python main.py")
        
        return True
    else:
        print(f"\n⚠️ {len(tests)-passed} tests failed")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
