"""
Test backtest with reduced data requirements.
"""
import sys
import os
import numpy as np
import pandas as pd

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_backtest_with_small_data():
    """Test backtesting with small dataset."""
    
    print("🔬 TESTING BACKTEST WITH SMALL DATA")
    print("=" * 50)
    
    try:
        from core.data_manager import DataManager
        from strategies.technical_strategies import MovingAverageCrossover
        from core.backtester import VectorizedBacktester
        
        # Create data manager
        print("   🔧 Creating components...")
        data_manager = DataManager()
        strategy = MovingAverageCrossover("BTCUSDT", "1h", fast_period=5, slow_period=10)  # Smaller periods
        backtester = VectorizedBacktester(initial_capital=10000)
        print("   ✅ Components created")
        
        # Generate more demo data
        print("   📊 Generating demo data...")
        demo_data = data_manager._generate_demo_data(
            "BTCUSDT", "1h", "2024-01-01", "2024-01-15"  # 2 weeks
        )
        
        print(f"   ✅ Demo data generated: {len(demo_data)} rows")
        
        if len(demo_data) >= 50:
            print("   🔬 Running backtest...")
            result = backtester.run_backtest(strategy, demo_data)
            
            print("   ✅ Backtest completed successfully!")
            print(f"   📊 Total Return: {result.total_return:.2%}")
            print(f"   📊 Sharpe Ratio: {result.sharpe_ratio:.4f}")
            print(f"   📊 Max Drawdown: {result.max_drawdown:.2%}")
            print(f"   📊 Total Trades: {result.total_trades}")
            print(f"   📊 Win Rate: {result.win_rate:.2%}")
            print(f"   ⏱️ Execution Time: {result.execution_time:.4f}s")
            
            return True
        else:
            print(f"   ❌ Still insufficient data: {len(demo_data)} < 50")
            return False
            
    except Exception as e:
        print(f"   ❌ Backtest failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_strategies():
    """Test multiple strategies with small data."""
    
    print("\n🎯 TESTING MULTIPLE STRATEGIES")
    print("=" * 50)
    
    try:
        from core.data_manager import DataManager
        from strategies.technical_strategies import (
            MovingAverageCrossover, 
            RSIMeanReversion,
            BollingerBandsMomentum
        )
        from core.backtester import VectorizedBacktester
        
        # Create components
        data_manager = DataManager()
        backtester = VectorizedBacktester(initial_capital=10000)
        
        # Generate data
        demo_data = data_manager._generate_demo_data(
            "BTCUSDT", "1h", "2024-01-01", "2024-01-20"  # 3 weeks
        )
        
        print(f"   📊 Generated {len(demo_data)} data points")
        
        # Test strategies
        strategies = [
            ("MA Crossover", MovingAverageCrossover("BTCUSDT", "1h", fast_period=5, slow_period=15)),
            ("RSI Mean Reversion", RSIMeanReversion("BTCUSDT", "1h", rsi_period=10, oversold_threshold=30, overbought_threshold=70)),
            ("Bollinger Bands", BollingerBandsMomentum("BTCUSDT", "1h", bb_period=15, bb_std=2.0))
        ]
        
        results = {}
        
        for name, strategy in strategies:
            try:
                print(f"   🎯 Testing {name}...")
                result = backtester.run_backtest(strategy, demo_data)
                
                results[name] = result
                print(f"      ✅ Return: {result.total_return:.2%}, Trades: {result.total_trades}")
                
            except Exception as e:
                print(f"      ❌ {name} failed: {e}")
        
        if results:
            print(f"\n   🏆 Successfully tested {len(results)} strategies!")
            
            # Find best strategy
            best_strategy = max(results.items(), key=lambda x: x[1].sharpe_ratio)
            print(f"   🥇 Best Strategy: {best_strategy[0]}")
            print(f"      Sharpe Ratio: {best_strategy[1].sharpe_ratio:.4f}")
            print(f"      Total Return: {best_strategy[1].total_return:.2%}")
            
            return True
        else:
            print("   ❌ No strategies completed successfully")
            return False
            
    except Exception as e:
        print(f"   ❌ Multiple strategy test failed: {e}")
        return False

def test_database_integration():
    """Test database integration with backtesting."""
    
    print("\n💾 TESTING DATABASE INTEGRATION")
    print("=" * 50)
    
    try:
        from core.data_manager import DataManager
        
        # Create data manager
        data_manager = DataManager()
        
        # Test getting data (should use database if available)
        print("   📊 Getting historical data...")
        data = data_manager.get_historical_data("BTCUSDT", "1h", "2024-01-01", "2024-01-10")
        
        print(f"   ✅ Retrieved {len(data)} data points")
        
        # Check if data was saved to database
        if len(data) > 0:
            print("   💾 Testing database save...")
            data_manager._save_to_database(data, "BTCUSDT", "1h")
            print("   ✅ Database save completed without errors")
            
            return True
        else:
            print("   ⚠️ No data to save")
            return False
            
    except Exception as e:
        print(f"   ❌ Database integration test failed: {e}")
        return False

def main():
    """Main test function."""
    
    print("🚀 BACKTEST FIX VERIFICATION")
    print("=" * 60)
    print("Testing reduced data requirements and database fixes")
    print()
    
    tests = [
        ("Backtest with Small Data", test_backtest_with_small_data),
        ("Multiple Strategies", test_multiple_strategies),
        ("Database Integration", test_database_integration)
    ]
    
    passed = 0
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n📊 RESULTS: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("\n🎉 ALL FIXES WORKING!")
        print("✅ Backtest data requirements reduced")
        print("✅ Database issues resolved")
        print("✅ Multiple strategies working")
        print("✅ System fully operational")
        
        print("\n🚀 READY FOR PRODUCTION:")
        print("   python main.py              # Full framework")
        print("   python evolution_runner.py  # Evolution system")
        print("   python minimal_framework.py # Minimal version")
        
        return True
    else:
        print(f"\n⚠️ {len(tests)-passed} tests failed")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
