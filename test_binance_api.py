"""
Test script for Binance API connection and data fetching.
No Redis, no localhost - only Binance API via .env configuration.
"""
import sys
import os
import traceback
from datetime import datetime, timedelta

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_binance_connection():
    """Test Binance API connection and data fetching."""
    
    print("🔍 TESTING BINANCE API CONNECTION")
    print("=" * 50)
    
    try:
        # Load environment variables
        print("📝 Loading configuration...")
        from dotenv import load_dotenv
        load_dotenv()
        
        api_key = os.getenv('BINANCE_API_KEY')
        api_secret = os.getenv('BINANCE_API_SECRET')
        testnet = os.getenv('BINANCE_TESTNET', 'true').lower() == 'true'
        
        if not api_key or not api_secret:
            print("❌ API credentials not found!")
            print("   Please run: python setup_binance.py")
            return False
        
        print(f"   ✅ API Key: {api_key[:8]}...")
        print(f"   ✅ Testnet: {testnet}")
        
        # Test Binance client
        print("\n📡 Testing Binance client...")
        from binance.client import Client
        
        client = Client(api_key, api_secret, testnet=testnet)
        print("   ✅ Client created successfully")
        
        # Test account info
        print("\n👤 Testing account access...")
        account = client.get_account()
        print(f"   ✅ Account Type: {account.get('accountType', 'Unknown')}")
        print(f"   ✅ Can Trade: {account.get('canTrade', False)}")
        
        # Test market data
        print("\n📊 Testing market data...")
        ticker = client.get_symbol_ticker(symbol="BTCUSDT")
        price = float(ticker['price'])
        print(f"   ✅ BTCUSDT Price: ${price:,.2f}")
        
        # Test historical data
        print("\n📈 Testing historical data...")
        end_time = datetime.now()
        start_time = end_time - timedelta(days=7)
        
        klines = client.get_historical_klines(
            "BTCUSDT",
            Client.KLINE_INTERVAL_1HOUR,
            start_time.strftime('%Y-%m-%d'),
            end_time.strftime('%Y-%m-%d')
        )
        
        print(f"   ✅ Retrieved {len(klines)} hourly candles")
        
        if klines:
            latest = klines[-1]
            latest_price = float(latest[4])  # Close price
            print(f"   ✅ Latest close: ${latest_price:,.2f}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("   Install with: pip install python-binance python-dotenv")
        return False
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        traceback.print_exc()
        return False

def test_framework_with_api():
    """Test framework components with real API data."""
    
    print("\n🧬 TESTING FRAMEWORK WITH REAL API DATA")
    print("=" * 50)
    
    try:
        # Test data manager
        print("📊 Testing DataManager...")
        from core.data_manager import DataManager
        
        data_manager = DataManager()
        print("   ✅ DataManager created")
        
        # Test data fetching
        print("   📈 Fetching BTCUSDT data...")
        data = data_manager.get_historical_data(
            "BTCUSDT", "1h", "2024-01-01", "2024-01-07"
        )
        
        if not data.empty:
            print(f"   ✅ Retrieved {len(data)} data points")
            print(f"   📊 Price range: ${data['close'].min():.2f} - ${data['close'].max():.2f}")
        else:
            print("   ⚠️ No data retrieved (using demo data)")
        
        # Test strategy
        print("\n🎯 Testing strategy with real data...")
        from strategies.technical_strategies import MovingAverageCrossover
        from core.backtester import VectorizedBacktester
        
        strategy = MovingAverageCrossover("BTCUSDT", "1h", fast_period=10, slow_period=30)
        print("   ✅ Strategy created")
        
        # Test backtester
        backtester = VectorizedBacktester(initial_capital=10000)
        
        if len(data) >= 100:
            result = backtester.run_backtest(strategy, data)
            print("   ✅ Backtest completed")
            print(f"   📊 Total Return: {result.total_return:.2%}")
            print(f"   📊 Sharpe Ratio: {result.sharpe_ratio:.4f}")
            print(f"   📊 Max Drawdown: {result.max_drawdown:.2%}")
            print(f"   📊 Total Trades: {result.total_trades}")
        else:
            print("   ⚠️ Insufficient data for backtest")
        
        return True
        
    except Exception as e:
        print(f"❌ Framework test failed: {e}")
        traceback.print_exc()
        return False

def test_multiple_symbols():
    """Test multiple symbols to verify API robustness."""
    
    print("\n🔄 TESTING MULTIPLE SYMBOLS")
    print("=" * 50)
    
    symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT"]
    
    try:
        from core.data_manager import DataManager
        data_manager = DataManager()
        
        for symbol in symbols:
            print(f"   📊 Testing {symbol}...")
            
            try:
                data = data_manager.get_historical_data(
                    symbol, "1h", "2024-01-01", "2024-01-03"
                )
                
                if not data.empty:
                    latest_price = data['close'].iloc[-1]
                    print(f"      ✅ {symbol}: ${latest_price:.2f} ({len(data)} points)")
                else:
                    print(f"      ⚠️ {symbol}: No data (demo mode)")
                    
            except Exception as e:
                print(f"      ❌ {symbol}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Multi-symbol test failed: {e}")
        return False

def main():
    """Main test function."""
    
    print("🚀 BINANCE API INTEGRATION TEST")
    print("=" * 60)
    print("Testing framework with real Binance API data")
    print("No Redis, no localhost - pure API integration")
    print()
    
    # Check if .env exists
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        print("   Please run: python setup_binance.py")
        return False
    
    tests = [
        ("Binance API Connection", test_binance_connection),
        ("Framework with API", test_framework_with_api),
        ("Multiple Symbols", test_multiple_symbols)
    ]
    
    passed = 0
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n📊 RESULTS: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Binance API integration is working perfectly!")
        print("✅ Framework is ready for real trading data!")
        print("\n🚀 READY TO RUN:")
        print("   python main.py              # Full framework")
        print("   python evolution_runner.py  # Evolution system")
        print("   python minimal_framework.py # Minimal version")
        
        return True
    else:
        print(f"\n⚠️ {len(tests)-passed} tests failed")
        print("Please check your API configuration and network connection.")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        traceback.print_exc()
        sys.exit(1)
