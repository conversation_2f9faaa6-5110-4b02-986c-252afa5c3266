"""
Quick test script to verify framework installation and basic functionality.
"""
import sys
import os
import traceback

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test if all required modules can be imported."""
    print("🔍 Testing imports...")
    
    try:
        # Test core imports
        from config.settings import settings
        print("  ✅ Config settings imported")
        
        from core.data_manager import DataManager
        print("  ✅ DataManager imported")
        
        from core.backtester import VectorizedBacktester
        print("  ✅ VectorizedBacktester imported")
        
        from strategies.base_strategy import BaseStrategy
        print("  ✅ BaseStrategy imported")
        
        from strategies.technical_strategies import MovingAverageCrossover
        print("  ✅ Technical strategies imported")
        
        from utils.metrics import AdvancedMetrics
        print("  ✅ Advanced metrics imported")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Import error: {e}")
        traceback.print_exc()
        return False


def test_basic_functionality():
    """Test basic framework functionality without API calls."""
    print("\n🧪 Testing basic functionality...")
    
    try:
        # Test strategy creation
        from strategies.technical_strategies import MovingAverageCrossover
        strategy = MovingAverageCrossover("BTCUSDT", "1h", fast_period=10, slow_period=30)
        print("  ✅ Strategy creation successful")
        
        # Test backtester creation
        from core.backtester import VectorizedBacktester
        backtester = VectorizedBacktester(initial_capital=10000)
        print("  ✅ Backtester creation successful")
        
        # Test metrics calculation with dummy data
        import numpy as np
        from utils.metrics import calculate_sharpe_ratio
        
        dummy_returns = np.random.normal(0.001, 0.02, 100)  # Dummy returns
        sharpe = calculate_sharpe_ratio(dummy_returns)
        print(f"  ✅ Metrics calculation successful (Sharpe: {sharpe:.4f})")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Functionality error: {e}")
        traceback.print_exc()
        return False


def test_dependencies():
    """Test if all required dependencies are available."""
    print("\n📦 Testing dependencies...")
    
    required_packages = [
        'numpy', 'pandas', 'numba', 'scipy',
        'binance', 'talib', 'redis', 'sqlalchemy',
        'sklearn', 'optuna', 'deap', 'loguru',
        'plotly', 'pydantic'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'binance':
                import binance
            elif package == 'talib':
                import talib
            elif package == 'sklearn':
                import sklearn
            else:
                __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install -r requirements.txt")
        return False
    
    return True


def test_configuration():
    """Test configuration loading."""
    print("\n⚙️  Testing configuration...")
    
    try:
        from config.settings import settings
        
        # Check if .env file exists
        if os.path.exists('.env'):
            print("  ✅ .env file found")
        else:
            print("  ⚠️  .env file not found (will use defaults)")
        
        # Test settings access
        print(f"  📊 Default symbol: {settings.default_symbol}")
        print(f"  ⏱️  Default interval: {settings.default_interval}")
        print(f"  💰 Max position size: {settings.max_position_size}")
        print(f"  🔧 JIT compilation: {settings.enable_jit_compilation}")
        print(f"  🚀 Parallel processing: {settings.parallel_processing}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Configuration error: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 Cryptocurrency Trading Framework - System Test")
    print("=" * 60)
    
    tests = [
        ("Dependencies", test_dependencies),
        ("Imports", test_imports),
        ("Configuration", test_configuration),
        ("Basic Functionality", test_basic_functionality)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Framework is ready to use.")
        print("\nNext steps:")
        print("1. Update your Binance API credentials in .env file")
        print("2. Run: python run_example.py")
        print("3. Or run: python main.py")
    else:
        print("\n⚠️  Some tests failed. Please check the errors above.")
        print("\nTroubleshooting:")
        print("1. Install missing dependencies: pip install -r requirements.txt")
        print("2. Check Python version (3.8+ recommended)")
        print("3. Ensure all files are in the correct directories")
    
    return passed == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
