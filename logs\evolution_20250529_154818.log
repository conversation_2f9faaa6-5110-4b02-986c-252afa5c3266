2025-05-29 15:48:18 | INFO     | 📝 Detailed logging to: logs/evolution_20250529_154818.log
2025-05-29 15:48:18 | INFO     | 🧬 Evolution Runner initialized
2025-05-29 15:48:18 | INFO     | 🚀 STARTING EVOLUTION CAMPAIGN: Demo_Evolution_v1
2025-05-29 15:48:18 | INFO     | ================================================================================
2025-05-29 15:48:18 | INFO     | 🎯 Target Symbols: ['BTCUSDT']
2025-05-29 15:48:18 | INFO     | ⏰ Target Timeframes: ['1h']
2025-05-29 15:48:18 | INFO     | 🧬 Evolution Config: {'evolution_generations': 10, 'population_size': 8, 'elite_ratio': 0.25, 'mutation_rate': 0.3, 'initial_capital': 50000}
2025-05-29 15:48:18 | INFO     | 🎯 Strategy Classes: ['MovingAverageCrossover', 'RSIMeanReversion', 'BollingerBandsMomentum', 'MACDStrategy']
2025-05-29 15:48:18 | INFO     | 
============================================================
2025-05-29 15:48:18 | INFO     | 🔬 EVOLVING STRATEGIES FOR BTCUSDT
2025-05-29 15:48:18 | INFO     | ============================================================
2025-05-29 15:48:18 | INFO     | 
🔄 Evolving BTCUSDT - 1h
2025-05-29 15:48:18 | INFO     | ----------------------------------------
2025-05-29 15:48:18 | INFO     | Performance tracking database initialized
2025-05-29 15:48:18 | INFO     | Evolution Engine initialized
2025-05-29 15:48:18 | INFO     | 📊 Data period: 2023-05-30 to 2025-05-29
2025-05-29 15:48:18 | INFO     | 🧬 Starting evolution with 4 strategy types
2025-05-29 15:48:18 | INFO     |    Symbols: ['BTCUSDT']
2025-05-29 15:48:18 | INFO     |    Timeframes: ['1h']
2025-05-29 15:48:18 | INFO     |    Generations: 10
2025-05-29 15:48:18 | INFO     | ✅ Initialized population with 8 strategies
2025-05-29 15:48:18 | INFO     | 
🔄 Generation 1/10
2025-05-29 15:48:18 | INFO     |    📊 Evaluating 8 strategies...
2025-05-29 15:48:18 | WARNING  | Strategy evaluation failed: Can't get local object 'WeakSet.__init__.<locals>._remove'
2025-05-29 15:48:18 | WARNING  | Strategy evaluation failed: Can't get local object 'WeakSet.__init__.<locals>._remove'
2025-05-29 15:48:18 | WARNING  | Strategy evaluation failed: Can't get local object 'WeakSet.__init__.<locals>._remove'
2025-05-29 15:48:18 | WARNING  | Strategy evaluation failed: Can't get local object 'WeakSet.__init__.<locals>._remove'
2025-05-29 15:48:18 | WARNING  | Strategy evaluation failed: Can't get local object 'WeakSet.__init__.<locals>._remove'
2025-05-29 15:48:18 | WARNING  | Strategy evaluation failed: Can't get local object 'WeakSet.__init__.<locals>._remove'
2025-05-29 15:48:18 | WARNING  | Strategy evaluation failed: Can't get local object 'WeakSet.__init__.<locals>._remove'
2025-05-29 15:48:18 | WARNING  | Strategy evaluation failed: Can't get local object 'WeakSet.__init__.<locals>._remove'
2025-05-29 15:48:18 | INFO     |    ✅ Successfully evaluated 0 strategies
2025-05-29 15:48:18 | INFO     |    🛡️ Calculating robustness metrics...
2025-05-29 15:48:18 | ERROR    | ❌ Evolution failed for BTCUSDT-1h: zero-size array to reduction operation maximum which has no identity
2025-05-29 15:48:18 | DEBUG    | Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Área de Trabalho\Claude 4.0 new frameworks\augment code\evolution_runner.py", line 228, in _evolve_symbol_strategies
    evolution_results = self.evolution_engine.evolve_strategies(
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Área de Trabalho\Claude 4.0 new frameworks\augment code\core\evolution_engine.py", line 139, in evolve_strategies
    generation_data = self._create_generation_record(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Área de Trabalho\Claude 4.0 new frameworks\augment code\core\evolution_engine.py", line 822, in _create_generation_record
    'max_fitness': np.max(fitness_scores),
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\numpy\_core\fromnumeric.py", line 3199, in max
    return _wrapreduction(a, np.maximum, 'max', axis, None, out,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\numpy\_core\fromnumeric.py", line 86, in _wrapreduction
    return ufunc.reduce(obj, axis, dtype, out, **passkwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ValueError: zero-size array to reduction operation maximum which has no identity

2025-05-29 15:48:18 | INFO     | ✅ Completed evolution for BTCUSDT
2025-05-29 15:48:18 | INFO     | 💾 Campaign results saved to: evolution_campaign_Demo_Evolution_v1_20250529_154818.json
2025-05-29 15:48:18 | INFO     | 
================================================================================
2025-05-29 15:48:18 | INFO     | 🏆 EVOLUTION CAMPAIGN FINAL REPORT
2025-05-29 15:48:18 | INFO     | ================================================================================
2025-05-29 15:48:18 | INFO     | Campaign: Demo_Evolution_v1
2025-05-29 15:48:18 | INFO     | Duration: 0.0 minutes
2025-05-29 15:48:18 | INFO     | Symbols: ['BTCUSDT']
2025-05-29 15:48:18 | INFO     | Timeframes: ['1h']
2025-05-29 15:48:18 | INFO     | 
📊 VALIDATION SUMMARY
