"""
Simple test to verify framework is working correctly.
"""
import sys
import os
import traceback

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """Test basic Python imports."""
    print("🔍 Testing basic imports...")
    
    try:
        import numpy as np
        print("   ✅ numpy")
    except ImportError:
        print("   ❌ numpy (required)")
        return False
    
    try:
        import pandas as pd
        print("   ✅ pandas")
    except ImportError:
        print("   ❌ pandas (required)")
        return False
    
    return True

def test_framework_imports():
    """Test framework imports."""
    print("\n🧪 Testing framework imports...")
    
    try:
        from config.settings import Settings
        settings = Settings()
        print("   ✅ Settings configuration")
    except Exception as e:
        print(f"   ❌ Settings configuration: {e}")
        return False
    
    try:
        from strategies.base_strategy import BaseStrategy, Signal, Position
        print("   ✅ Base strategy classes")
    except Exception as e:
        print(f"   ❌ Base strategy classes: {e}")
        return False
    
    try:
        from strategies.technical_strategies import MovingAverageCrossover
        print("   ✅ Technical strategies")
    except Exception as e:
        print(f"   ❌ Technical strategies: {e}")
        return False
    
    try:
        from core.backtester import VectorizedBacktester
        print("   ✅ Backtester")
    except Exception as e:
        print(f"   ❌ Backtester: {e}")
        return False
    
    try:
        from utils.metrics import calculate_sharpe_ratio
        print("   ✅ Metrics")
    except Exception as e:
        print(f"   ❌ Metrics: {e}")
        return False
    
    return True

def test_basic_functionality():
    """Test basic framework functionality."""
    print("\n⚡ Testing basic functionality...")
    
    try:
        import numpy as np
        import pandas as pd
        from strategies.technical_strategies import MovingAverageCrossover
        from core.backtester import VectorizedBacktester
        from utils.metrics import calculate_sharpe_ratio
        
        # Create sample data
        np.random.seed(42)
        dates = pd.date_range(start='2023-01-01', periods=100, freq='1H')
        prices = 30000 + np.cumsum(np.random.randn(100) * 10)
        
        data = pd.DataFrame(index=dates)
        data['close'] = prices
        data['open'] = data['close'].shift(1).fillna(data['close'].iloc[0])
        data['high'] = np.maximum(data['open'], data['close']) * 1.001
        data['low'] = np.minimum(data['open'], data['close']) * 0.999
        data['volume'] = np.random.uniform(100, 1000, len(data))
        
        print("   ✅ Sample data created")
        
        # Test strategy creation
        strategy = MovingAverageCrossover("BTCUSDT", "1h", fast_period=5, slow_period=10)
        print("   ✅ Strategy created")
        
        # Test strategy initialization
        strategy.initialize(data.head(20))
        print("   ✅ Strategy initialized")
        
        # Test signal generation
        signal = strategy.process_data(data.head(50))
        print(f"   ✅ Signal generated: {signal.action}")
        
        # Test backtester creation
        backtester = VectorizedBacktester(initial_capital=10000)
        print("   ✅ Backtester created")
        
        # Test metrics calculation
        returns = np.random.normal(0.001, 0.02, 50)
        sharpe = calculate_sharpe_ratio(returns)
        print(f"   ✅ Metrics calculated: Sharpe = {sharpe:.4f}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Functionality test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run simple test."""
    print("🚀 SIMPLE FRAMEWORK TEST")
    print("=" * 40)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Framework Imports", test_framework_imports),
        ("Basic Functionality", test_basic_functionality)
    ]
    
    passed = 0
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n📊 Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("\n🎉 ALL TESTS PASSED!")
        print("Framework is working correctly!")
        print("\nYou can now run:")
        print("   python start.py")
        print("   python auto_run.py")
        return True
    else:
        print(f"\n⚠️ {len(tests)-passed} tests failed")
        print("Please check the errors above")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
