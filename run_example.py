"""
Example script demonstrating the trading framework capabilities.
Shows how to run backtests, optimize strategies, and compare performance.
"""
import sys
import os
import time
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import TradingFramework
from strategies.technical_strategies import MovingAverageCrossover, RSIMeanReversion
from loguru import logger


def run_quick_example():
    """Run a quick example to demonstrate framework capabilities."""

    print("🚀 Cryptocurrency Trading Framework - Quick Example")
    print("=" * 60)

    # Initialize framework
    framework = TradingFramework()

    # Test with a smaller dataset for quick demonstration
    print("\n📊 Running strategy comparison...")
    print("Symbol: BTCUSDT | Timeframe: 4h | Period: 6 months")

    try:
        results = framework.run_strategy_comparison(
            symbol="BTCUSDT",
            timeframe="4h",  # 4-hour timeframe for faster execution
            start_date="2023-06-01",  # 6 months of data
            end_date="2023-12-01"
        )

        if results:
            print("\n✅ Example completed successfully!")
            print("\nKey Results:")

            for strategy_name, strategy_results in results.items():
                optimized = strategy_results['optimized']
                print(f"\n{strategy_name}:")
                print(f"  📈 Total Return: {optimized.total_return:.2%}")
                print(f"  ⚡ Sharpe Ratio: {optimized.sharpe_ratio:.4f}")
                print(f"  📉 Max Drawdown: {optimized.max_drawdown:.2%}")
                print(f"  🎯 Win Rate: {optimized.win_rate:.2%}")
                print(f"  🔄 Total Trades: {optimized.total_trades}")
                print(f"  ⏱️  Execution Time: {optimized.execution_time:.4f}s")

        else:
            print("❌ No results generated. Check your API credentials and internet connection.")

    except Exception as e:
        print(f"❌ Error running example: {e}")
        print("\nTroubleshooting tips:")
        print("1. Check your Binance API credentials in .env file")
        print("2. Ensure you have internet connection")
        print("3. Verify all dependencies are installed: pip install -r requirements.txt")


def run_single_strategy_test():
    """Test a single strategy with detailed output."""

    print("\n🔬 Single Strategy Test - Moving Average Crossover")
    print("=" * 60)

    from core.data_manager import DataManager
    from core.backtester import VectorizedBacktester

    # Initialize components
    data_manager = DataManager()
    backtester = VectorizedBacktester(initial_capital=10000)

    try:
        # Get data
        print("📥 Fetching historical data...")
        data = data_manager.get_historical_data(
            symbol="BTCUSDT",
            interval="1h",
            start_date="2023-10-01",
            end_date="2023-11-01"
        )

        if data.empty:
            print("❌ No data received. Check API credentials.")
            return

        print(f"✅ Loaded {len(data)} data points")

        # Create strategy
        strategy = MovingAverageCrossover(
            symbol="BTCUSDT",
            timeframe="1h",
            fast_period=10,
            slow_period=30,
            trend_period=100
        )

        # Run backtest
        print("🔄 Running backtest...")
        start_time = time.time()
        result = backtester.run_backtest(strategy, data)
        execution_time = time.time() - start_time

        # Display results
        print(f"\n📊 Backtest Results (executed in {execution_time:.4f}s):")
        print(f"  💰 Initial Capital: ${result.initial_capital:,.2f}")
        print(f"  💰 Final Capital: ${result.final_capital:,.2f}")
        print(f"  📈 Total Return: {result.total_return:.2%}")
        print(f"  📊 Annual Return: {result.annual_return:.2%}")
        print(f"  ⚡ Sharpe Ratio: {result.sharpe_ratio:.4f}")
        print(f"  📉 Max Drawdown: {result.max_drawdown:.2%}")
        print(f"  🎯 Win Rate: {result.win_rate:.2%}")
        print(f"  💹 Profit Factor: {result.profit_factor:.2f}")
        print(f"  🔄 Total Trades: {result.total_trades}")
        print(f"  ⏱️  Avg Trade Duration: {result.avg_trade_duration:.2f} hours")

        # Show some trades
        if result.trades:
            print(f"\n📋 Sample Trades (showing first 5 of {len(result.trades)}):")
            for i, trade in enumerate(result.trades[:5]):
                pnl_pct = trade.get('pnl_percentage', 0)
                side = trade.get('side', 'UNKNOWN')
                entry_price = trade.get('entry_price', 0)
                exit_price = trade.get('exit_price', 0)
                print(f"  {i+1}. {side} | Entry: ${entry_price:.2f} | Exit: ${exit_price:.2f} | P&L: {pnl_pct:.2f}%")

        print("\n✅ Single strategy test completed!")

    except Exception as e:
        print(f"❌ Error in single strategy test: {e}")


def run_optimization_example():
    """Demonstrate strategy optimization."""

    print("\n🧬 Strategy Optimization Example")
    print("=" * 60)

    from core.data_manager import DataManager
    from core.backtester import VectorizedBacktester
    from core.optimizer import StrategyOptimizer

    # Initialize components
    data_manager = DataManager()
    backtester = VectorizedBacktester(initial_capital=10000)
    optimizer = StrategyOptimizer(data_manager, backtester)

    try:
        print("🔍 Optimizing RSI Mean Reversion strategy...")

        # Define parameter ranges
        parameter_ranges = {
            'rsi_period': (10, 20),
            'oversold_threshold': (25, 35),
            'overbought_threshold': (65, 75)
        }

        # Run optimization (small number of trials for demo)
        optimization_result = optimizer.optimize_strategy(
            strategy_class=RSIMeanReversion,
            symbol="BTCUSDT",
            timeframe="4h",
            parameter_ranges=parameter_ranges,
            start_date="2023-08-01",
            end_date="2023-11-01",
            method='bayesian',
            n_trials=20  # Small number for quick demo
        )

        print(f"\n🏆 Optimization Results:")
        print(f"  🎯 Best Score: {optimization_result.best_score:.4f}")
        print(f"  ⚙️  Best Parameters: {optimization_result.best_parameters}")
        print(f"  🛡️  Robustness Score: {optimization_result.robustness_score:.4f}")
        print(f"  ⏱️  Execution Time: {optimization_result.execution_time:.2f}s")
        print(f"  🔄 Total Evaluations: {optimization_result.total_evaluations}")

        # Show walk-forward results
        if optimization_result.walk_forward_results:
            wf_returns = [r.total_return for r in optimization_result.walk_forward_results]
            print(f"\n📊 Walk-Forward Analysis ({len(wf_returns)} periods):")
            print(f"  📈 Average Return: {sum(wf_returns)/len(wf_returns):.2%}")
            print(f"  📊 Best Period: {max(wf_returns):.2%}")
            print(f"  📉 Worst Period: {min(wf_returns):.2%}")
            print(f"  ✅ Positive Periods: {sum(1 for r in wf_returns if r > 0)}/{len(wf_returns)}")

        print("\n✅ Optimization example completed!")

    except Exception as e:
        print(f"❌ Error in optimization example: {e}")


def run_automated_demo():
    """Run automated demo with real data - no user input required."""
    print("🚀 Running Automated Demo with Real Binance Data")
    print("=" * 60)

    # Check if API credentials are configured
    try:
        from config.settings import settings
        if (settings.binance_api_key == "your_binance_api_key_here" or
            not settings.binance_api_key or not settings.binance_api_secret):
            print("⚠️ Binance API credentials not configured!")
            print("   Using fallback demo with sample data...")
            run_quick_example()
            return
    except:
        print("⚠️ Configuration error - using sample data demo...")
        run_quick_example()
        return

    print("✅ API credentials found - using real Binance data")

    # Run all demos automatically
    demos = [
        ("🔬 Single Strategy Test", run_single_strategy_test),
        ("🧬 Strategy Optimization", run_optimization_example),
        ("🚀 Quick Strategy Comparison", run_quick_example),
    ]

    for demo_name, demo_func in demos:
        print(f"\n{'='*60}")
        print(f"Running {demo_name}")
        print(f"{'='*60}")

        try:
            demo_func()
            print(f"✅ {demo_name} completed successfully")
        except Exception as e:
            print(f"❌ {demo_name} failed: {e}")

        print(f"\n⏱️ Waiting 3 seconds before next demo...")
        import time
        time.sleep(3)

    print(f"\n🎉 All demos completed!")
    print("Next steps:")
    print("1. Run full analysis: python auto_run.py")
    print("2. Run main framework: python main.py")


def main():
    """Main function - runs automatically without user input."""

    print("🎯 Cryptocurrency Trading Framework - Automated Examples")
    print("This framework focuses on:")
    print("  ⚡ Ultra-fast execution (< 1ms signal generation)")
    print("  🛡️  Real-world robustness (walk-forward analysis)")
    print("  🧬 Iterative improvement (genetic optimization)")
    print("  📊 Comprehensive metrics (15+ performance indicators)")
    print("\n🤖 Running in AUTOMATED mode - no user input required!")

    # Run automated demo
    run_automated_demo()


if __name__ == "__main__":
    main()
