"""
Quick test to verify the framework is working.
"""
import sys
import os
import time
import traceback

def test_imports():
    """Test basic imports."""
    print("🔍 Testing imports...")
    
    try:
        import numpy as np
        print("   ✅ numpy")
        
        import pandas as pd
        print("   ✅ pandas")
        
        # Test a simple calculation
        data = np.random.randn(100)
        mean = np.mean(data)
        std = np.std(data)
        print(f"   ✅ numpy calculation: mean={mean:.4f}, std={std:.4f}")
        
        # Test pandas
        df = pd.DataFrame({'values': data})
        print(f"   ✅ pandas dataframe: {len(df)} rows")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Import failed: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Calculation failed: {e}")
        return False

def test_simple_strategy():
    """Test a very simple trading strategy."""
    print("\n🎯 Testing simple strategy...")
    
    try:
        import numpy as np
        
        # Generate simple price data
        np.random.seed(42)
        prices = [100]
        for i in range(100):
            change = np.random.normal(0, 0.02)
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 1))  # Ensure positive
        
        print(f"   ✅ Generated {len(prices)} prices")
        print(f"   📊 Price range: ${min(prices):.2f} - ${max(prices):.2f}")
        
        # Simple moving average strategy
        def simple_ma(data, period):
            if len(data) < period:
                return None
            return sum(data[-period:]) / period
        
        # Test signals
        signals = []
        for i in range(20, len(prices)):
            ma_short = simple_ma(prices[:i], 5)
            ma_long = simple_ma(prices[:i], 20)
            
            if ma_short and ma_long:
                if ma_short > ma_long:
                    signals.append('BUY')
                elif ma_short < ma_long:
                    signals.append('SELL')
                else:
                    signals.append('HOLD')
        
        buy_signals = signals.count('BUY')
        sell_signals = signals.count('SELL')
        hold_signals = signals.count('HOLD')
        
        print(f"   ✅ Generated {len(signals)} signals")
        print(f"   📈 BUY: {buy_signals}, SELL: {sell_signals}, HOLD: {hold_signals}")
        
        # Simple backtest
        capital = 10000
        position = 0
        trades = 0
        
        for i, signal in enumerate(signals):
            price = prices[i + 20]  # Offset for MA calculation
            
            if signal == 'BUY' and position == 0:
                # Buy
                position = capital / price
                capital = 0
                trades += 1
            elif signal == 'SELL' and position > 0:
                # Sell
                capital = position * price
                position = 0
                trades += 1
        
        # Final value
        final_value = capital + (position * prices[-1])
        total_return = (final_value - 10000) / 10000
        
        print(f"   ✅ Backtest completed")
        print(f"   💰 Initial: $10,000")
        print(f"   💰 Final: ${final_value:.2f}")
        print(f"   📊 Return: {total_return:.2%}")
        print(f"   🔄 Trades: {trades}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Strategy test failed: {e}")
        traceback.print_exc()
        return False

def test_metrics():
    """Test basic metrics calculation."""
    print("\n📊 Testing metrics...")
    
    try:
        import numpy as np
        
        # Generate returns
        returns = np.random.normal(0.001, 0.02, 252)  # Daily returns for 1 year
        
        # Calculate basic metrics
        total_return = np.sum(returns)
        annual_return = (1 + np.mean(returns)) ** 252 - 1
        volatility = np.std(returns) * np.sqrt(252)
        
        # Sharpe ratio
        sharpe_ratio = np.sqrt(252) * np.mean(returns) / np.std(returns)
        
        # Max drawdown
        equity_curve = np.cumprod(1 + returns)
        running_max = np.maximum.accumulate(equity_curve)
        drawdowns = (equity_curve - running_max) / running_max
        max_drawdown = np.min(drawdowns)
        
        print(f"   ✅ Total Return: {total_return:.2%}")
        print(f"   ✅ Annual Return: {annual_return:.2%}")
        print(f"   ✅ Volatility: {volatility:.2%}")
        print(f"   ✅ Sharpe Ratio: {sharpe_ratio:.4f}")
        print(f"   ✅ Max Drawdown: {max_drawdown:.2%}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Metrics test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🚀 QUICK FRAMEWORK TEST")
    print("=" * 50)
    
    start_time = time.time()
    
    tests = [
        ("Basic Imports", test_imports),
        ("Simple Strategy", test_simple_strategy),
        ("Basic Metrics", test_metrics)
    ]
    
    passed = 0
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    execution_time = time.time() - start_time
    
    print(f"\n📊 RESULTS:")
    print(f"   Tests Passed: {passed}/{len(tests)}")
    print(f"   Execution Time: {execution_time:.2f} seconds")
    
    if passed == len(tests):
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Framework core functionality is working!")
        print("\n🚀 READY FOR EVOLUTION:")
        print("   - Basic calculations: ✅")
        print("   - Strategy logic: ✅") 
        print("   - Performance metrics: ✅")
        print("   - Data processing: ✅")
        
        print("\n📈 NEXT STEPS:")
        print("   1. Install full dependencies: pip install python-binance loguru")
        print("   2. Configure Binance API keys in .env file")
        print("   3. Run full evolution: python evolution_runner.py")
        print("   4. Or use minimal version: python minimal_framework.py")
        
        return True
    else:
        print(f"\n⚠️ {len(tests)-passed} TESTS FAILED")
        print("Please check the errors above and install missing dependencies.")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        traceback.print_exc()
        sys.exit(1)
