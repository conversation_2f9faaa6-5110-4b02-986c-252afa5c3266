"""
Test if the framework is ready to run with current dependencies.
"""
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_python():
    """Test basic Python functionality."""
    print("🐍 Testing Python environment...")
    
    # Test Python version
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"   ✅ Python {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"   ❌ Python {version.major}.{version.minor} (need 3.8+)")
        return False

def test_core_imports():
    """Test core Python imports."""
    print("\n📦 Testing core imports...")
    
    try:
        import sqlite3
        print("   ✅ sqlite3")
    except ImportError:
        print("   ❌ sqlite3")
        return False
    
    try:
        import json
        print("   ✅ json")
    except ImportError:
        print("   ❌ json")
        return False
    
    try:
        import datetime
        print("   ✅ datetime")
    except ImportError:
        print("   ❌ datetime")
        return False
    
    return True

def test_framework_structure():
    """Test if framework files exist."""
    print("\n📁 Testing framework structure...")
    
    required_files = [
        'config/settings.py',
        'strategies/base_strategy.py',
        'strategies/technical_strategies.py',
        'core/backtester.py',
        'utils/metrics.py',
        '.env'
    ]
    
    missing = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path}")
            missing.append(file_path)
    
    return len(missing) == 0

def test_dependencies():
    """Test if required dependencies are available."""
    print("\n🔍 Testing dependencies...")
    
    dependencies = [
        ('numpy', 'NumPy'),
        ('pandas', 'Pandas'),
        ('numba', 'Numba'),
        ('scipy', 'SciPy'),
        ('binance', 'Python-Binance'),
        ('sqlalchemy', 'SQLAlchemy'),
        ('dotenv', 'Python-Dotenv'),
        ('pydantic', 'Pydantic'),
        ('loguru', 'Loguru'),
        ('tqdm', 'TQDM')
    ]
    
    available = 0
    for module, name in dependencies:
        try:
            __import__(module)
            print(f"   ✅ {name}")
            available += 1
        except ImportError:
            print(f"   ❌ {name} (missing)")
    
    print(f"\n   📊 Available: {available}/{len(dependencies)}")
    return available >= len(dependencies) - 2  # Allow 2 missing

def test_framework_imports():
    """Test framework-specific imports."""
    print("\n🧪 Testing framework imports...")
    
    try:
        from config.settings import settings
        print("   ✅ Settings configuration")
    except Exception as e:
        print(f"   ❌ Settings configuration: {e}")
        return False
    
    try:
        from strategies.base_strategy import BaseStrategy
        print("   ✅ Base strategy")
    except Exception as e:
        print(f"   ❌ Base strategy: {e}")
        return False
    
    try:
        from strategies.technical_strategies import MovingAverageCrossover
        print("   ✅ Technical strategies")
    except Exception as e:
        print(f"   ❌ Technical strategies: {e}")
        return False
    
    try:
        from core.backtester import VectorizedBacktester
        print("   ✅ Backtester")
    except Exception as e:
        print(f"   ❌ Backtester: {e}")
        return False
    
    try:
        from utils.metrics import calculate_sharpe_ratio
        print("   ✅ Metrics")
    except Exception as e:
        print(f"   ❌ Metrics: {e}")
        return False
    
    return True

def test_basic_functionality():
    """Test basic framework functionality."""
    print("\n⚡ Testing basic functionality...")
    
    try:
        # Test strategy creation
        from strategies.technical_strategies import MovingAverageCrossover
        strategy = MovingAverageCrossover("BTCUSDT", "1h")
        print("   ✅ Strategy creation")
        
        # Test backtester creation
        from core.backtester import VectorizedBacktester
        backtester = VectorizedBacktester(initial_capital=10000)
        print("   ✅ Backtester creation")
        
        # Test metrics
        from utils.metrics import calculate_sharpe_ratio
        import numpy as np
        returns = np.array([0.01, -0.005, 0.02, 0.001, -0.01])
        sharpe = calculate_sharpe_ratio(returns)
        print(f"   ✅ Metrics calculation (Sharpe: {sharpe:.4f})")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Functionality test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 CRYPTOCURRENCY TRADING FRAMEWORK - READINESS TEST")
    print("=" * 70)
    
    tests = [
        ("Python Environment", test_basic_python),
        ("Core Imports", test_core_imports),
        ("Framework Structure", test_framework_structure),
        ("Dependencies", test_dependencies),
        ("Framework Imports", test_framework_imports),
        ("Basic Functionality", test_basic_functionality)
    ]
    
    passed = 0
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                passed += 1
        except Exception as e:
            print(f"   ❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 70)
    print("📋 TEST SUMMARY")
    print("=" * 70)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:25} {status}")
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 FRAMEWORK IS READY!")
        print("\nYou can now run:")
        print("   python start.py      # Complete setup and execution")
        print("   python auto_run.py   # Advanced analysis")
        print("   python main.py       # Main framework")
        
    elif passed >= len(results) - 2:
        print("\n⚠️ FRAMEWORK IS MOSTLY READY!")
        print("Some optional features may not work, but core functionality is available.")
        print("\nYou can try running:")
        print("   python verify_framework.py")
        
    else:
        print("\n❌ FRAMEWORK NOT READY")
        print("Please install missing dependencies:")
        print("   pip install numpy pandas numba scipy python-binance")
        print("   pip install sqlalchemy python-dotenv pydantic loguru tqdm")
    
    return passed >= len(results) - 1

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
