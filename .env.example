# Binance API Configuration
# Get your API keys from: https://www.binance.com/en/my/settings/api-management
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_API_SECRET=your_binance_secret_key_here
BINANCE_TESTNET=true

# Trading Configuration
DEFAULT_SYMBOL=BTCUSDT
DEFAULT_INTERVAL=1h
MAX_CONCURRENT_STRATEGIES=10
BACKTEST_START_DATE=2020-01-01
BACKTEST_END_DATE=2024-01-01

# Performance Settings (disabled for compatibility)
ENABLE_JIT_COMPILATION=false
CACHE_ENABLED=false
PARALLEL_PROCESSING=false
MAX_WORKERS=4

# Risk Management
MAX_POSITION_SIZE=0.1
STOP_LOSS_PERCENTAGE=0.02
TAKE_PROFIT_PERCENTAGE=0.06
MAX_DRAWDOWN=0.15

# Database (local SQLite)
DATABASE_URL=sqlite:///trading_data.db

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/trading.log
