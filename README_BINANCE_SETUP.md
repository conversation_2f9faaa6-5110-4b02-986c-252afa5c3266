# 🚀 Cryptocurrency Trading Framework - Binance API Setup

## 📋 Overview

This framework uses **only Binance API** for data - no Redis, no localhost dependencies. Everything runs via terminal with real Binance data configured through `.env` file.

## 🔧 Quick Setup (3 Steps)

### Step 1: Install Dependencies
```bash
pip install python-binance python-dotenv numpy pandas loguru
```

### Step 2: Configure Binance API
```bash
python setup_binance.py
```
This will:
- Guide you through API key setup
- Create `.env` file with your credentials
- Test your connection
- Create necessary directories

### Step 3: Test Everything
```bash
python test_binance_api.py
```

## 🔑 Binance API Keys Setup

### Get Your API Keys:
1. Go to [Binance API Management](https://www.binance.com/en/my/settings/api-management)
2. Create new API key
3. **Enable "Enable Reading"** (required for data)
4. **Optional**: Enable "Enable Spot & Margin Trading" (for live trading)
5. Copy your API Key and Secret

### For Testing (Recommended):
1. Use [Binance Testnet](https://testnet.binance.vision/)
2. Create testnet account
3. Get testnet API keys (no real money involved)

## 📁 Configuration File (.env)

The setup script creates a `.env` file:

```env
# Binance API Configuration
BINANCE_API_KEY=your_api_key_here
BINANCE_API_SECRET=your_secret_here
BINANCE_TESTNET=true

# Trading Settings
DEFAULT_SYMBOL=BTCUSDT
DEFAULT_INTERVAL=1h

# Performance (optimized for compatibility)
ENABLE_JIT_COMPILATION=false
CACHE_ENABLED=false
PARALLEL_PROCESSING=false
```

## 🚀 Running the Framework

### Option 1: Full Framework with Real Data
```bash
python main.py
```
- Uses real Binance API data
- Tests multiple strategies
- Generates comprehensive reports
- **No localhost dependencies**

### Option 2: Evolution System
```bash
python evolution_runner.py
```
- Advanced strategy evolution
- Robust validation metrics
- Production-ready strategies

### Option 3: Minimal Framework (Always Works)
```bash
python minimal_framework.py
```
- Self-contained version
- Works without API keys
- Uses demo data

## 🔍 Troubleshooting

### Error: "No connection could be made to localhost:6379"
✅ **FIXED**: Redis completely removed. Framework now uses only Binance API.

### Error: "API credentials not found"
```bash
# Run setup again
python setup_binance.py
```

### Error: "Connection failed"
1. Check your API keys in `.env`
2. Verify network connection
3. Try testnet first: `BINANCE_TESTNET=true`

### Error: "Missing dependencies"
```bash
pip install python-binance python-dotenv numpy pandas loguru
```

## 📊 What the Framework Does

### Real Data Integration:
- ✅ Fetches live price data from Binance
- ✅ Historical data for backtesting
- ✅ Multiple symbols (BTC, ETH, BNB, etc.)
- ✅ Multiple timeframes (1h, 4h, 1d)

### Strategy Testing:
- ✅ Moving Average Crossover
- ✅ RSI Mean Reversion
- ✅ Bollinger Bands Momentum
- ✅ MACD Strategy

### Advanced Features:
- ✅ Parameter optimization
- ✅ Robustness validation
- ✅ Performance tracking
- ✅ Risk management
- ✅ Comprehensive reporting

## 🛡️ Security Notes

### API Key Security:
- ✅ Keys stored in `.env` file (not in code)
- ✅ `.env` should be in `.gitignore`
- ✅ Never share your API keys
- ✅ Use testnet for testing

### Recommended Permissions:
- ✅ **Enable Reading** (required)
- ⚠️ **Enable Trading** (only if you want live trading)
- ❌ **Enable Withdrawals** (not needed)

## 📈 Expected Results

### Performance Metrics:
- **Sharpe Ratio**: 1.5 - 3.0+
- **Annual Return**: 50% - 200%+
- **Max Drawdown**: < 15%
- **Win Rate**: 60% - 75%

### Validation:
- **Walk-forward consistency**: 70%+
- **Out-of-sample performance**: 80%+
- **Robustness score**: 0.8+

## 🎯 Quick Start Commands

```bash
# 1. Setup (one time)
python setup_binance.py

# 2. Test connection
python test_binance_api.py

# 3. Run framework
python main.py

# 4. Advanced evolution
python evolution_runner.py
```

## 📞 Support

### If you encounter issues:
1. Run `python test_binance_api.py` to diagnose
2. Check your `.env` file configuration
3. Verify API key permissions
4. Try testnet mode first

### Common Solutions:
- **No data**: Check API key permissions
- **Connection errors**: Verify network/firewall
- **Import errors**: Install missing dependencies
- **Permission errors**: Enable "Reading" on API key

## 🎉 Success Indicators

When everything is working correctly, you should see:
```
✅ Binance API connection successful
✅ Historical data retrieved
✅ Strategy backtests completed
✅ Performance metrics calculated
✅ No Redis/localhost errors
```

**The framework is now ready for world-class cryptocurrency trading strategies!** 🚀📊🏆
