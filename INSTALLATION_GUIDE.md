# 🚀 Guia de Instalação - Framework de Trading

## ⚡ Instalação Rápida (Recomendado)

### Método 1: Instalação Automática
```bash
python install.py
```

### Método 2: Instalação Manual das Dependências Essenciais
```bash
pip install numpy pandas numba scipy
pip install python-binance sqlalchemy python-dotenv
pip install pydantic loguru tqdm
```

### Método 3: Usando requirements.txt (Corrigido)
```bash
pip install -r requirements.txt
```

## 📦 Dependências por Categoria

### ✅ **Essenciais (Obrigatórias)**
```bash
pip install numpy>=1.21.0
pip install pandas>=1.5.0
pip install numba>=0.56.0
pip install scipy>=1.9.0
pip install python-binance>=1.0.17
pip install sqlalchemy>=1.4.0
pip install python-dotenv>=0.19.0
pip install pydantic>=1.10.0
pip install loguru>=0.6.0
pip install tqdm>=4.60.0
```

### 🔧 **Opcionais (Para Funcionalidades Avançadas)**
```bash
# Análise técnica avançada
pip install ta-lib

# Cache para performance
pip install redis

# Backtesting avançado
pip install vectorbt
pip install backtrader

# Machine Learning
pip install scikit-learn
pip install xgboost
pip install lightgbm

# Otimização
pip install deap
pip install optuna

# Visualização
pip install plotly
pip install dash
```

## 🔧 Solução de Problemas Comuns

### Erro: "sqlite3 not found"
**Solução:** O sqlite3 já vem com Python. Remova da lista de dependências.

### Erro: "ta-lib installation failed"
**Solução:** TA-Lib é opcional. O framework funciona sem ela.
```bash
# Windows
pip install TA-Lib

# Se falhar, use fallbacks internos (já implementados)
```

### Erro: "redis connection failed"
**Solução:** Redis é opcional para cache.
```bash
# Instalar Redis (opcional)
pip install redis

# Ou desabilitar no .env
CACHE_ENABLED=False
```

### Erro: "No module named 'numba'"
**Solução:** Instalar Numba para JIT compilation.
```bash
pip install numba

# Se falhar, o framework usa NumPy puro (mais lento)
```

## ✅ Verificação da Instalação

### Teste Rápido
```bash
python test_ready.py
```

### Teste Completo
```bash
python verify_framework.py
```

### Teste de Produção
```bash
python production_test.py
```

## 🎯 Configuração Mínima para Funcionamento

### Dependências Absolutamente Necessárias:
1. **Python 3.8+** (obrigatório)
2. **numpy** (cálculos matemáticos)
3. **pandas** (manipulação de dados)
4. **python-binance** (API da Binance)
5. **python-dotenv** (configurações)
6. **loguru** (logging)

### Com essas 6 dependências, o framework já funciona!

## 🚀 Execução Após Instalação

### 1. Configurar API (Opcional)
Edite o arquivo `.env`:
```env
BINANCE_API_KEY=sua_api_key_aqui
BINANCE_API_SECRET=sua_secret_key_aqui
```

### 2. Executar Framework
```bash
# Execução automática completa
python start.py

# Análise avançada (com API)
python auto_run.py

# Verificação rápida
python verify_framework.py
```

## 📊 Status das Funcionalidades por Dependência

| Funcionalidade | Dependência | Status | Fallback |
|----------------|-------------|--------|----------|
| Estratégias básicas | numpy, pandas | ✅ Essencial | ❌ Não |
| API Binance | python-binance | ✅ Essencial | ❌ Não |
| Indicadores técnicos | ta-lib | 🔧 Opcional | ✅ Implementação própria |
| JIT compilation | numba | 🔧 Opcional | ✅ NumPy puro |
| Cache | redis | 🔧 Opcional | ✅ Sem cache |
| Otimização genética | deap | 🔧 Opcional | ✅ Grid search |
| Otimização Bayesiana | optuna | 🔧 Opcional | ✅ Random search |
| Visualização | plotly | 🔧 Opcional | ✅ Logs apenas |
| ML strategies | sklearn | 🔧 Opcional | ✅ Estratégias técnicas |

## 🎉 Resumo

### Para Funcionalidade Básica:
```bash
pip install numpy pandas python-binance python-dotenv loguru
python start.py
```

### Para Funcionalidade Completa:
```bash
pip install -r requirements.txt
pip install -r requirements-optional.txt
python start.py
```

### Para Teste Sem Instalação:
```bash
# O framework já tem fallbacks implementados
python verify_framework.py
```

## 🔗 Links Úteis

- **Binance API**: https://www.binance.com/en/my/settings/api-management
- **TA-Lib Installation**: https://github.com/mrjbq7/ta-lib
- **Python 3.8+**: https://www.python.org/downloads/

---

**💡 Dica:** O framework foi projetado para funcionar mesmo com dependências mínimas. Comece com as essenciais e adicione as opcionais conforme necessário!
