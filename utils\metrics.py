"""
Advanced performance metrics and risk calculations.
Optimized for speed with Numba JIT compilation.
"""
import numpy as np
import pandas as pd
from typing import Tuple, Dict, Any
import warnings
warnings.filterwarnings('ignore')

# Try to import Numba, fallback to no-op decorator if not available
try:
    from numba import jit
    NUMBA_AVAILABLE = True
except ImportError:
    NUMBA_AVAILABLE = False
    def jit(nopython=True):
        """Fallback decorator when Numba is not available."""
        def decorator(func):
            return func
        return decorator


@jit(nopython=True)
def calculate_sharpe_ratio(returns: np.ndarray, risk_free_rate: float = 0.0) -> float:
    """Calculate Sharpe ratio with JIT compilation."""
    if len(returns) == 0:
        return 0.0

    excess_returns = returns - risk_free_rate / 252  # Daily risk-free rate

    if np.std(excess_returns) == 0:
        return 0.0

    return np.sqrt(252) * np.mean(excess_returns) / np.std(excess_returns)


@jit(nopython=True)
def calculate_sortino_ratio(returns: np.ndarray, risk_free_rate: float = 0.0) -> float:
    """Calculate Sortino ratio (downside deviation)."""
    if len(returns) == 0:
        return 0.0

    excess_returns = returns - risk_free_rate / 252
    downside_returns = excess_returns[excess_returns < 0]

    if len(downside_returns) == 0 or np.std(downside_returns) == 0:
        return 0.0

    downside_std = np.sqrt(np.mean(downside_returns ** 2))
    return np.sqrt(252) * np.mean(excess_returns) / downside_std


@jit(nopython=True)
def calculate_max_drawdown(equity_curve: np.ndarray) -> Tuple[float, int, int]:
    """
    Calculate maximum drawdown and its duration.
    Returns: (max_drawdown, start_index, end_index)
    """
    if len(equity_curve) == 0:
        return 0.0, 0, 0

    peak = equity_curve[0]
    max_dd = 0.0
    max_dd_start = 0
    max_dd_end = 0
    current_dd_start = 0

    for i in range(len(equity_curve)):
        if equity_curve[i] > peak:
            peak = equity_curve[i]
            current_dd_start = i

        if peak > 0:
            drawdown = (peak - equity_curve[i]) / peak
        else:
            drawdown = 0.0

        if drawdown > max_dd:
            max_dd = drawdown
            max_dd_start = current_dd_start
            max_dd_end = i

    return max_dd, max_dd_start, max_dd_end


@jit(nopython=True)
def calculate_calmar_ratio(returns: np.ndarray, equity_curve: np.ndarray) -> float:
    """Calculate Calmar ratio (Annual return / Max drawdown)."""
    if len(returns) == 0 or len(equity_curve) == 0:
        return 0.0

    annual_return = (1 + np.mean(returns)) ** 252 - 1
    max_dd, _, _ = calculate_max_drawdown(equity_curve)

    if max_dd == 0:
        return 0.0

    return annual_return / max_dd


@jit(nopython=True)
def calculate_var(returns: np.ndarray, confidence_level: float = 0.05) -> float:
    """Calculate Value at Risk."""
    if len(returns) == 0:
        return 0.0

    sorted_returns = np.sort(returns)
    index = int(confidence_level * len(sorted_returns))

    if index >= len(sorted_returns):
        return sorted_returns[-1]

    return sorted_returns[index]


@jit(nopython=True)
def calculate_cvar(returns: np.ndarray, confidence_level: float = 0.05) -> float:
    """Calculate Conditional Value at Risk (Expected Shortfall)."""
    if len(returns) == 0:
        return 0.0

    var = calculate_var(returns, confidence_level)
    tail_returns = returns[returns <= var]

    if len(tail_returns) == 0:
        return var

    return np.mean(tail_returns)


@jit(nopython=True)
def calculate_omega_ratio(returns: np.ndarray, threshold: float = 0.0) -> float:
    """Calculate Omega ratio."""
    if len(returns) == 0:
        return 0.0

    excess_returns = returns - threshold
    positive_returns = excess_returns[excess_returns > 0]
    negative_returns = excess_returns[excess_returns < 0]

    if len(negative_returns) == 0:
        return np.inf if len(positive_returns) > 0 else 1.0

    if len(positive_returns) == 0:
        return 0.0

    return np.sum(positive_returns) / abs(np.sum(negative_returns))


@jit(nopython=True)
def calculate_information_ratio(returns: np.ndarray, benchmark_returns: np.ndarray) -> float:
    """Calculate Information ratio."""
    if len(returns) == 0 or len(benchmark_returns) == 0:
        return 0.0

    min_length = min(len(returns), len(benchmark_returns))
    active_returns = returns[:min_length] - benchmark_returns[:min_length]

    if np.std(active_returns) == 0:
        return 0.0

    return np.sqrt(252) * np.mean(active_returns) / np.std(active_returns)


class AdvancedMetrics:
    """Advanced performance metrics calculator."""

    @staticmethod
    def calculate_all_metrics(
        returns: np.ndarray,
        equity_curve: np.ndarray,
        benchmark_returns: np.ndarray = None,
        risk_free_rate: float = 0.02
    ) -> Dict[str, float]:
        """Calculate comprehensive set of performance metrics."""

        if len(returns) == 0:
            return {metric: 0.0 for metric in [
                'total_return', 'annual_return', 'volatility', 'sharpe_ratio',
                'sortino_ratio', 'calmar_ratio', 'max_drawdown', 'var_5',
                'cvar_5', 'omega_ratio', 'skewness', 'kurtosis'
            ]}

        # Basic metrics
        total_return = (equity_curve[-1] - equity_curve[0]) / equity_curve[0] if len(equity_curve) > 0 else 0
        annual_return = (1 + np.mean(returns)) ** 252 - 1
        volatility = np.std(returns) * np.sqrt(252)

        # Risk-adjusted metrics
        sharpe = calculate_sharpe_ratio(returns, risk_free_rate)
        sortino = calculate_sortino_ratio(returns, risk_free_rate)
        calmar = calculate_calmar_ratio(returns, equity_curve)

        # Drawdown metrics
        max_dd, _, _ = calculate_max_drawdown(equity_curve)

        # Risk metrics
        var_5 = calculate_var(returns, 0.05)
        cvar_5 = calculate_cvar(returns, 0.05)
        omega = calculate_omega_ratio(returns)

        # Distribution metrics
        skewness = AdvancedMetrics._calculate_skewness(returns)
        kurtosis = AdvancedMetrics._calculate_kurtosis(returns)

        metrics = {
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe,
            'sortino_ratio': sortino,
            'calmar_ratio': calmar,
            'max_drawdown': max_dd,
            'var_5': var_5,
            'cvar_5': cvar_5,
            'omega_ratio': omega,
            'skewness': skewness,
            'kurtosis': kurtosis
        }

        # Add benchmark comparison if provided
        if benchmark_returns is not None:
            info_ratio = calculate_information_ratio(returns, benchmark_returns)
            metrics['information_ratio'] = info_ratio

            # Beta calculation
            if len(benchmark_returns) >= len(returns):
                beta = AdvancedMetrics._calculate_beta(returns, benchmark_returns[:len(returns)])
                metrics['beta'] = beta

                # Alpha calculation
                benchmark_annual = (1 + np.mean(benchmark_returns[:len(returns)])) ** 252 - 1
                alpha = annual_return - (risk_free_rate + beta * (benchmark_annual - risk_free_rate))
                metrics['alpha'] = alpha

        return metrics

    @staticmethod
    @jit(nopython=True)
    def _calculate_skewness(returns: np.ndarray) -> float:
        """Calculate skewness of returns."""
        if len(returns) < 3:
            return 0.0

        mean_return = np.mean(returns)
        std_return = np.std(returns)

        if std_return == 0:
            return 0.0

        skew = np.mean(((returns - mean_return) / std_return) ** 3)
        return skew

    @staticmethod
    @jit(nopython=True)
    def _calculate_kurtosis(returns: np.ndarray) -> float:
        """Calculate kurtosis of returns."""
        if len(returns) < 4:
            return 0.0

        mean_return = np.mean(returns)
        std_return = np.std(returns)

        if std_return == 0:
            return 0.0

        kurt = np.mean(((returns - mean_return) / std_return) ** 4) - 3
        return kurt

    @staticmethod
    @jit(nopython=True)
    def _calculate_beta(returns: np.ndarray, benchmark_returns: np.ndarray) -> float:
        """Calculate beta relative to benchmark."""
        if len(returns) != len(benchmark_returns) or len(returns) < 2:
            return 1.0

        covariance = np.cov(returns, benchmark_returns)[0, 1]
        benchmark_variance = np.var(benchmark_returns)

        if benchmark_variance == 0:
            return 1.0

        return covariance / benchmark_variance

    @staticmethod
    def calculate_rolling_metrics(
        returns: pd.Series,
        window: int = 252,
        metrics: list = None
    ) -> pd.DataFrame:
        """Calculate rolling performance metrics."""

        if metrics is None:
            metrics = ['sharpe_ratio', 'volatility', 'max_drawdown']

        rolling_metrics = pd.DataFrame(index=returns.index)

        for metric in metrics:
            if metric == 'sharpe_ratio':
                rolling_metrics[metric] = returns.rolling(window).apply(
                    lambda x: calculate_sharpe_ratio(x.values), raw=False
                )
            elif metric == 'volatility':
                rolling_metrics[metric] = returns.rolling(window).std() * np.sqrt(252)
            elif metric == 'max_drawdown':
                # Calculate rolling max drawdown
                equity = (1 + returns).cumprod()
                rolling_max = equity.rolling(window).max()
                rolling_dd = (equity - rolling_max) / rolling_max
                rolling_metrics[metric] = rolling_dd.rolling(window).min().abs()

        return rolling_metrics

    @staticmethod
    def calculate_trade_metrics(trades: pd.DataFrame) -> Dict[str, float]:
        """Calculate trade-specific metrics."""

        if trades.empty:
            return {}

        # Basic trade metrics
        total_trades = len(trades)
        winning_trades = len(trades[trades['pnl'] > 0])
        losing_trades = len(trades[trades['pnl'] < 0])

        win_rate = winning_trades / total_trades if total_trades > 0 else 0

        # P&L metrics
        total_pnl = trades['pnl'].sum()
        avg_win = trades[trades['pnl'] > 0]['pnl'].mean() if winning_trades > 0 else 0
        avg_loss = trades[trades['pnl'] < 0]['pnl'].mean() if losing_trades > 0 else 0

        # Profit factor
        gross_profit = trades[trades['pnl'] > 0]['pnl'].sum()
        gross_loss = abs(trades[trades['pnl'] < 0]['pnl'].sum())
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0

        # Duration metrics
        if 'duration' in trades.columns:
            avg_duration = trades['duration'].mean()
            avg_winning_duration = trades[trades['pnl'] > 0]['duration'].mean() if winning_trades > 0 else pd.Timedelta(0)
            avg_losing_duration = trades[trades['pnl'] < 0]['duration'].mean() if losing_trades > 0 else pd.Timedelta(0)
        else:
            avg_duration = avg_winning_duration = avg_losing_duration = pd.Timedelta(0)

        # Consecutive metrics
        consecutive_wins = AdvancedMetrics._calculate_consecutive_wins(trades['pnl'].values)
        consecutive_losses = AdvancedMetrics._calculate_consecutive_losses(trades['pnl'].values)

        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'avg_duration_hours': avg_duration.total_seconds() / 3600 if avg_duration else 0,
            'avg_winning_duration_hours': avg_winning_duration.total_seconds() / 3600 if avg_winning_duration else 0,
            'avg_losing_duration_hours': avg_losing_duration.total_seconds() / 3600 if avg_losing_duration else 0,
            'max_consecutive_wins': consecutive_wins,
            'max_consecutive_losses': consecutive_losses,
            'largest_win': trades['pnl'].max(),
            'largest_loss': trades['pnl'].min(),
            'expectancy': total_pnl / total_trades if total_trades > 0 else 0
        }

    @staticmethod
    @jit(nopython=True)
    def _calculate_consecutive_wins(pnl_array: np.ndarray) -> int:
        """Calculate maximum consecutive wins."""
        max_consecutive = 0
        current_consecutive = 0

        for pnl in pnl_array:
            if pnl > 0:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0

        return max_consecutive

    @staticmethod
    @jit(nopython=True)
    def _calculate_consecutive_losses(pnl_array: np.ndarray) -> int:
        """Calculate maximum consecutive losses."""
        max_consecutive = 0
        current_consecutive = 0

        for pnl in pnl_array:
            if pnl < 0:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0

        return max_consecutive

    @staticmethod
    def calculate_real_world_metrics(returns: np.ndarray, equity_curve: np.ndarray) -> Dict[str, float]:
        """Calculate real-world validation metrics for robust strategy assessment."""

        real_world_metrics = {}

        # Consistency metrics
        real_world_metrics['return_consistency'] = AdvancedMetrics._calculate_return_consistency(returns)
        real_world_metrics['profit_factor'] = AdvancedMetrics._calculate_profit_factor(returns)
        real_world_metrics['recovery_factor'] = AdvancedMetrics._calculate_recovery_factor(returns, equity_curve)

        # Stability metrics
        real_world_metrics['stability_ratio'] = AdvancedMetrics._calculate_stability_ratio(equity_curve)
        real_world_metrics['ulcer_index'] = AdvancedMetrics._calculate_ulcer_index(equity_curve)
        real_world_metrics['pain_index'] = AdvancedMetrics._calculate_pain_index(equity_curve)

        # Tail risk metrics
        real_world_metrics['tail_ratio'] = AdvancedMetrics._calculate_tail_ratio(returns)
        real_world_metrics['worst_month'] = AdvancedMetrics._calculate_worst_period(returns, 30)
        real_world_metrics['worst_quarter'] = AdvancedMetrics._calculate_worst_period(returns, 90)

        # Market stress metrics
        real_world_metrics['stress_test_score'] = AdvancedMetrics._calculate_stress_test_score(returns)
        real_world_metrics['crisis_performance'] = AdvancedMetrics._calculate_crisis_performance(returns)

        # Robustness score (composite)
        real_world_metrics['robustness_score'] = AdvancedMetrics._calculate_composite_robustness(real_world_metrics)

        return real_world_metrics

    @staticmethod
    def _calculate_return_consistency(returns: np.ndarray) -> float:
        """Calculate return consistency score."""
        if len(returns) < 30:
            return 0.0

        # Monthly return consistency
        monthly_returns = []
        for i in range(0, len(returns), 30):
            month_returns = returns[i:i+30]
            if len(month_returns) >= 20:  # At least 20 days
                monthly_returns.append(np.sum(month_returns))

        if len(monthly_returns) < 3:
            return 0.0

        positive_months = sum(1 for r in monthly_returns if r > 0)
        consistency = positive_months / len(monthly_returns)

        # Adjust for volatility
        monthly_std = np.std(monthly_returns)
        monthly_mean = np.mean(monthly_returns)

        if monthly_mean > 0:
            volatility_penalty = 1 / (1 + monthly_std / abs(monthly_mean))
        else:
            volatility_penalty = 0.5

        return consistency * volatility_penalty

    @staticmethod
    def _calculate_profit_factor(returns: np.ndarray) -> float:
        """Calculate profit factor (gross profit / gross loss)."""
        positive_returns = returns[returns > 0]
        negative_returns = returns[returns < 0]

        if len(negative_returns) == 0:
            return 10.0  # Cap at 10 if no losses

        gross_profit = np.sum(positive_returns)
        gross_loss = abs(np.sum(negative_returns))

        if gross_loss == 0:
            return 10.0

        return gross_profit / gross_loss

    @staticmethod
    def _calculate_recovery_factor(returns: np.ndarray, equity_curve: np.ndarray) -> float:
        """Calculate recovery factor (total return / max drawdown)."""
        if len(equity_curve) < 2:
            return 0.0

        total_return = (equity_curve[-1] - equity_curve[0]) / equity_curve[0]
        max_dd, _, _ = calculate_max_drawdown(equity_curve)

        if max_dd == 0:
            return 100.0  # Cap if no drawdown

        return total_return / max_dd

    @staticmethod
    def _calculate_stability_ratio(equity_curve: np.ndarray) -> float:
        """Calculate stability ratio based on equity curve smoothness."""
        if len(equity_curve) < 10:
            return 0.0

        # Calculate R-squared of equity curve vs linear trend
        x = np.arange(len(equity_curve))
        y = equity_curve

        # Linear regression coefficients
        n = len(x)
        sum_x = np.sum(x)
        sum_y = np.sum(y)
        sum_xy = np.sum(x * y)
        sum_x2 = np.sum(x * x)

        # Calculate slope and intercept
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        intercept = (sum_y - slope * sum_x) / n

        # Calculate R-squared
        y_pred = slope * x + intercept
        ss_res = np.sum((y - y_pred) ** 2)
        ss_tot = np.sum((y - np.mean(y)) ** 2)

        if ss_tot == 0:
            return 1.0

        r_squared = 1 - (ss_res / ss_tot)
        return max(0.0, r_squared)

    @staticmethod
    def _calculate_ulcer_index(equity_curve: np.ndarray) -> float:
        """Calculate Ulcer Index (measure of downside risk)."""
        if len(equity_curve) < 2:
            return 0.0

        # Calculate running maximum
        running_max = np.maximum.accumulate(equity_curve)

        # Calculate percentage drawdowns
        drawdowns = (equity_curve - running_max) / running_max

        # Ulcer Index is RMS of drawdowns
        ulcer_index = np.sqrt(np.mean(drawdowns ** 2))

        return ulcer_index

    @staticmethod
    def _calculate_pain_index(equity_curve: np.ndarray) -> float:
        """Calculate Pain Index (average of squared drawdowns)."""
        if len(equity_curve) < 2:
            return 0.0

        # Calculate running maximum
        running_max = np.maximum.accumulate(equity_curve)

        # Calculate percentage drawdowns
        drawdowns = (equity_curve - running_max) / running_max

        # Pain Index is mean of squared drawdowns
        pain_index = np.mean(drawdowns ** 2)

        return pain_index

    @staticmethod
    def _calculate_tail_ratio(returns: np.ndarray) -> float:
        """Calculate tail ratio (95th percentile / 5th percentile)."""
        if len(returns) < 20:
            return 1.0

        p95 = np.percentile(returns, 95)
        p5 = np.percentile(returns, 5)

        if p5 >= 0:
            return 10.0  # Cap if no negative tail

        return abs(p95 / p5)

    @staticmethod
    def _calculate_worst_period(returns: np.ndarray, period_length: int) -> float:
        """Calculate worst period return."""
        if len(returns) < period_length:
            return np.sum(returns)

        worst_return = 0
        for i in range(len(returns) - period_length + 1):
            period_return = np.sum(returns[i:i + period_length])
            worst_return = min(worst_return, period_return)

        return worst_return

    @staticmethod
    def _calculate_stress_test_score(returns: np.ndarray) -> float:
        """Calculate stress test score based on extreme scenarios."""
        if len(returns) < 50:
            return 0.5

        # Identify extreme negative days (bottom 5%)
        extreme_threshold = np.percentile(returns, 5)
        extreme_days = returns <= extreme_threshold

        if np.sum(extreme_days) == 0:
            return 1.0

        # Calculate average return on extreme days
        extreme_returns = returns[extreme_days]
        avg_extreme_return = np.mean(extreme_returns)

        # Score based on how well strategy handles extreme events
        # Better performance on extreme days = higher score
        stress_score = 1 / (1 + abs(avg_extreme_return) * 10)

        return stress_score

    @staticmethod
    def _calculate_crisis_performance(returns: np.ndarray) -> float:
        """Calculate performance during crisis periods (high volatility)."""
        if len(returns) < 100:
            return 0.5

        # Calculate rolling volatility
        window = 30
        rolling_vol = []

        for i in range(window, len(returns)):
            period_vol = np.std(returns[i-window:i])
            rolling_vol.append(period_vol)

        if not rolling_vol:
            return 0.5

        rolling_vol = np.array(rolling_vol)

        # Identify crisis periods (top 20% volatility)
        crisis_threshold = np.percentile(rolling_vol, 80)
        crisis_periods = rolling_vol >= crisis_threshold

        if np.sum(crisis_periods) == 0:
            return 0.5

        # Calculate returns during crisis periods
        crisis_returns = []
        for i, is_crisis in enumerate(crisis_periods):
            if is_crisis:
                crisis_returns.extend(returns[i+window-30:i+window])

        if not crisis_returns:
            return 0.5

        # Score based on crisis performance
        avg_crisis_return = np.mean(crisis_returns)
        crisis_sharpe = avg_crisis_return / (np.std(crisis_returns) + 1e-8)

        # Normalize to 0-1 scale
        crisis_score = 1 / (1 + abs(crisis_sharpe - 0.5))

        return crisis_score

    @staticmethod
    def _calculate_composite_robustness(metrics: Dict[str, float]) -> float:
        """Calculate composite robustness score from individual metrics."""

        # Weight different aspects of robustness
        weights = {
            'return_consistency': 0.20,
            'profit_factor': 0.15,
            'recovery_factor': 0.15,
            'stability_ratio': 0.15,
            'ulcer_index': 0.10,  # Lower is better, so invert
            'pain_index': 0.10,   # Lower is better, so invert
            'tail_ratio': 0.05,
            'stress_test_score': 0.05,
            'crisis_performance': 0.05
        }

        # Normalize metrics to 0-1 scale
        normalized_metrics = {}

        # Consistency (0-1, higher is better)
        normalized_metrics['return_consistency'] = min(1.0, max(0.0, metrics.get('return_consistency', 0)))

        # Profit factor (normalize to 0-1, cap at 5)
        pf = metrics.get('profit_factor', 1)
        normalized_metrics['profit_factor'] = min(1.0, max(0.0, (pf - 1) / 4))

        # Recovery factor (normalize to 0-1, cap at 10)
        rf = metrics.get('recovery_factor', 0)
        normalized_metrics['recovery_factor'] = min(1.0, max(0.0, rf / 10))

        # Stability ratio (0-1, higher is better)
        normalized_metrics['stability_ratio'] = min(1.0, max(0.0, metrics.get('stability_ratio', 0)))

        # Ulcer index (invert - lower is better)
        ui = metrics.get('ulcer_index', 0.5)
        normalized_metrics['ulcer_index'] = max(0.0, 1 - min(1.0, ui))

        # Pain index (invert - lower is better)
        pi = metrics.get('pain_index', 0.5)
        normalized_metrics['pain_index'] = max(0.0, 1 - min(1.0, pi))

        # Tail ratio (normalize to 0-1, cap at 5)
        tr = metrics.get('tail_ratio', 1)
        normalized_metrics['tail_ratio'] = min(1.0, max(0.0, (tr - 1) / 4))

        # Stress test score (0-1, higher is better)
        normalized_metrics['stress_test_score'] = min(1.0, max(0.0, metrics.get('stress_test_score', 0.5)))

        # Crisis performance (0-1, higher is better)
        normalized_metrics['crisis_performance'] = min(1.0, max(0.0, metrics.get('crisis_performance', 0.5)))

        # Calculate weighted composite score
        composite_score = 0.0
        for metric, weight in weights.items():
            composite_score += normalized_metrics.get(metric, 0.5) * weight

        return composite_score