"""
Automated execution script for the cryptocurrency trading framework.
Runs completely automatically with real Binance data - no user input required.
"""
import sys
import os
import time
from datetime import datetime, timedelta
import traceback

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from loguru import logger
from main import TradingFramework


def setup_logging():
    """Setup comprehensive logging."""
    # Remove default logger
    logger.remove()
    
    # Add console logger with colors
    logger.add(
        sys.stdout,
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{message}</cyan>",
        colorize=True
    )
    
    # Add file logger
    log_file = f"logs/auto_run_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    os.makedirs("logs", exist_ok=True)
    
    logger.add(
        log_file,
        level="DEBUG",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {message}",
        rotation="10 MB",
        retention="30 days"
    )
    
    logger.info(f"📝 Logging to: {log_file}")


def check_prerequisites():
    """Check if all prerequisites are met."""
    logger.info("🔍 Checking prerequisites...")
    
    # Check .env file
    if not os.path.exists('.env'):
        logger.error("❌ .env file not found! Please create it with your Binance API credentials.")
        return False
    
    # Check API credentials
    try:
        from config.settings import settings
        if not settings.binance_api_key or not settings.binance_api_secret:
            logger.error("❌ Binance API credentials not configured in .env file!")
            return False
        
        if settings.binance_api_key == "your_binance_api_key_here":
            logger.error("❌ Please update .env file with your real Binance API credentials!")
            return False
            
        logger.info("✅ API credentials configured")
        
    except Exception as e:
        logger.error(f"❌ Error checking configuration: {e}")
        return False
    
    # Test Binance connection
    try:
        from core.data_manager import DataManager
        data_manager = DataManager()
        
        # Try to get latest price
        price = data_manager.get_latest_price("BTCUSDT")
        if price > 0:
            logger.info(f"✅ Binance connection successful - BTC price: ${price:,.2f}")
        else:
            logger.error("❌ Failed to get price from Binance")
            return False
            
    except Exception as e:
        logger.error(f"❌ Binance connection failed: {e}")
        return False
    
    return True


def run_comprehensive_analysis():
    """Run comprehensive automated analysis."""
    logger.info("🚀 Starting Comprehensive Cryptocurrency Trading Analysis")
    logger.info("=" * 80)
    
    start_time = time.time()
    
    # Initialize framework
    framework = TradingFramework()
    
    # Define test configurations - multiple symbols and timeframes
    test_configs = [
        # Major cryptocurrencies - 1 year data
        {"symbol": "BTCUSDT", "timeframe": "1h", "start": "2023-01-01", "end": "2024-01-01", "priority": 1},
        {"symbol": "ETHUSDT", "timeframe": "1h", "start": "2023-01-01", "end": "2024-01-01", "priority": 1},
        {"symbol": "BNBUSDT", "timeframe": "1h", "start": "2023-01-01", "end": "2024-01-01", "priority": 2},
        
        # Different timeframes for BTC
        {"symbol": "BTCUSDT", "timeframe": "4h", "start": "2022-01-01", "end": "2024-01-01", "priority": 1},
        {"symbol": "BTCUSDT", "timeframe": "1d", "start": "2020-01-01", "end": "2024-01-01", "priority": 2},
        
        # Additional altcoins
        {"symbol": "ADAUSDT", "timeframe": "1h", "start": "2023-01-01", "end": "2024-01-01", "priority": 3},
        {"symbol": "SOLUSDT", "timeframe": "1h", "start": "2023-01-01", "end": "2024-01-01", "priority": 3},
        {"symbol": "DOTUSDT", "timeframe": "1h", "start": "2023-01-01", "end": "2024-01-01", "priority": 3},
    ]
    
    # Sort by priority (1 = highest priority)
    test_configs.sort(key=lambda x: x['priority'])
    
    all_results = {}
    successful_tests = 0
    failed_tests = 0
    
    for i, config in enumerate(test_configs, 1):
        logger.info(f"\n🔍 Test {i}/{len(test_configs)}: {config['symbol']} - {config['timeframe']}")
        logger.info(f"   Period: {config['start']} to {config['end']}")
        logger.info(f"   Priority: {config['priority']}")
        logger.info("-" * 60)
        
        try:
            # Run strategy comparison
            results = framework.run_strategy_comparison(
                symbol=config["symbol"],
                timeframe=config["timeframe"],
                start_date=config["start"],
                end_date=config["end"]
            )
            
            if results:
                test_key = f"{config['symbol']}_{config['timeframe']}"
                all_results[test_key] = results
                successful_tests += 1
                
                # Log best result for this test
                best_strategy = max(
                    results.items(),
                    key=lambda x: x[1]['optimized'].sharpe_ratio * x[1]['optimization'].robustness_score
                )
                
                logger.info(f"✅ Completed {config['symbol']} - {config['timeframe']}")
                logger.info(f"   Best Strategy: {best_strategy[0]}")
                logger.info(f"   Sharpe Ratio: {best_strategy[1]['optimized'].sharpe_ratio:.4f}")
                logger.info(f"   Total Return: {best_strategy[1]['optimized'].total_return:.2%}")
                logger.info(f"   Robustness: {best_strategy[1]['optimization'].robustness_score:.4f}")
                
            else:
                logger.warning(f"⚠️ No results for {config['symbol']} - {config['timeframe']}")
                failed_tests += 1
                
        except Exception as e:
            logger.error(f"❌ Error testing {config['symbol']}: {e}")
            logger.debug(traceback.format_exc())
            failed_tests += 1
            continue
        
        # Progress update
        elapsed = time.time() - start_time
        avg_time_per_test = elapsed / i
        remaining_tests = len(test_configs) - i
        estimated_remaining = avg_time_per_test * remaining_tests
        
        logger.info(f"⏱️ Progress: {i}/{len(test_configs)} | "
                   f"Elapsed: {elapsed/60:.1f}m | "
                   f"ETA: {estimated_remaining/60:.1f}m")
    
    # Generate comprehensive final report
    total_time = time.time() - start_time
    
    logger.info(f"\n{'='*100}")
    logger.info("🏁 ANALYSIS COMPLETED")
    logger.info(f"{'='*100}")
    logger.info(f"⏱️ Total Execution Time: {total_time/60:.1f} minutes")
    logger.info(f"✅ Successful Tests: {successful_tests}")
    logger.info(f"❌ Failed Tests: {failed_tests}")
    logger.info(f"📊 Success Rate: {successful_tests/(successful_tests+failed_tests)*100:.1f}%")
    
    if all_results:
        # Generate detailed final report
        framework._generate_final_report(all_results)
        
        # Find and highlight best overall strategy
        best_overall = framework._find_best_strategy(all_results)
        if best_overall:
            logger.info(f"\n🏆 CHAMPION STRATEGY")
            logger.info(f"{'='*50}")
            logger.info(f"   Strategy: {best_overall['name']}")
            logger.info(f"   Symbol: {best_overall['symbol']}")
            logger.info(f"   Timeframe: {best_overall['timeframe']}")
            logger.info(f"   Combined Score: {best_overall['score']:.4f}")
            logger.info(f"   Sharpe Ratio: {best_overall['sharpe']:.4f}")
            logger.info(f"   Total Return: {best_overall['return']:.2%}")
            logger.info(f"   Max Drawdown: {best_overall['max_dd']:.2%}")
            logger.info(f"   Robustness Score: {best_overall['robustness']:.4f}")
            logger.info(f"   Optimal Parameters: {best_overall['parameters']}")
            
            # Save best strategy configuration
            save_best_strategy_config(best_overall)
            
        # Generate summary statistics
        generate_summary_statistics(all_results)
        
    else:
        logger.error("❌ No successful results generated!")
        logger.error("   Check your API credentials and internet connection")
    
    logger.info(f"\n🎯 Automated analysis completed!")
    logger.info(f"📝 Detailed logs saved to logs/ directory")
    
    return all_results


def save_best_strategy_config(best_strategy: dict):
    """Save the best strategy configuration for future use."""
    import json
    
    config = {
        "best_strategy": best_strategy,
        "timestamp": datetime.now().isoformat(),
        "framework_version": "1.0.0"
    }
    
    config_file = f"best_strategy_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2, default=str)
    
    logger.info(f"💾 Best strategy configuration saved to: {config_file}")


def generate_summary_statistics(all_results: dict):
    """Generate and log summary statistics."""
    logger.info(f"\n📈 SUMMARY STATISTICS")
    logger.info(f"{'='*50}")
    
    # Collect all strategy results
    all_strategies = []
    for symbol_tf, results in all_results.items():
        for strategy_name, strategy_data in results.items():
            optimized = strategy_data['optimized']
            optimization = strategy_data['optimization']
            
            all_strategies.append({
                'name': strategy_name,
                'symbol_tf': symbol_tf,
                'sharpe': optimized.sharpe_ratio,
                'return': optimized.total_return,
                'max_dd': optimized.max_drawdown,
                'win_rate': optimized.win_rate,
                'robustness': optimization.robustness_score
            })
    
    if all_strategies:
        # Calculate statistics
        sharpe_ratios = [s['sharpe'] for s in all_strategies]
        returns = [s['return'] for s in all_strategies]
        robustness_scores = [s['robustness'] for s in all_strategies]
        
        logger.info(f"   Total Strategies Tested: {len(all_strategies)}")
        logger.info(f"   Average Sharpe Ratio: {sum(sharpe_ratios)/len(sharpe_ratios):.4f}")
        logger.info(f"   Average Return: {sum(returns)/len(returns):.2%}")
        logger.info(f"   Average Robustness: {sum(robustness_scores)/len(robustness_scores):.4f}")
        logger.info(f"   Best Sharpe Ratio: {max(sharpe_ratios):.4f}")
        logger.info(f"   Best Return: {max(returns):.2%}")
        logger.info(f"   Strategies with Positive Returns: {sum(1 for r in returns if r > 0)}/{len(returns)}")


def main():
    """Main automated execution function."""
    print("🚀 Cryptocurrency Trading Framework - Automated Execution")
    print("=" * 70)
    print("This script runs completely automatically with real Binance data")
    print("No user input required - just wait for results!")
    print("=" * 70)
    
    # Setup logging
    setup_logging()
    
    # Check prerequisites
    if not check_prerequisites():
        logger.error("❌ Prerequisites not met. Exiting.")
        return False
    
    # Run comprehensive analysis
    try:
        results = run_comprehensive_analysis()
        
        if results:
            logger.info("🎉 Analysis completed successfully!")
            return True
        else:
            logger.error("❌ Analysis failed - no results generated")
            return False
            
    except KeyboardInterrupt:
        logger.warning("⚠️ Analysis interrupted by user")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        logger.debug(traceback.format_exc())
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
