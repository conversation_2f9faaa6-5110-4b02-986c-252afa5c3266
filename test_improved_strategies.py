"""
Test script for improved trading strategies.
Verifies that the enhanced strategies generate more trades and better performance.
"""
import sys
import os
import time
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from loguru import logger
from core.data_manager import DataManager
from core.backtester import VectorizedBacktester
from strategies.technical_strategies import (
    MovingAverageCrossover,
    RSIMeanReversion,
    BollingerBandsMomentum,
    MACDStrategy,
    AdvancedMomentumStrategy
)


def test_improved_strategies():
    """Test all improved strategies with optimized parameters."""
    print("🚀 TESTING IMPROVED TRADING STRATEGIES")
    print("=" * 60)
    
    # Initialize components
    data_manager = DataManager()
    backtester = VectorizedBacktester(initial_capital=10000)
    
    # Generate demo data
    print("\n📊 Generating demo data...")
    data = data_manager._generate_demo_data('BTCUSDT', '1h', '2024-01-01', '2024-01-20')
    print(f"   ✅ Generated {len(data)} data points")
    
    # Define improved strategies with optimized parameters
    strategies = [
        {
            'name': 'Enhanced MA Crossover',
            'strategy': MovingAverageCrossover(
                'BTCUSDT', '1h', 
                fast_period=8, slow_period=21, trend_period=50, min_trend_strength=0.005
            )
        },
        {
            'name': 'Adaptive RSI',
            'strategy': RSIMeanReversion(
                'BTCUSDT', '1h',
                rsi_period=12, oversold_threshold=35, overbought_threshold=65, trend_filter=False
            )
        },
        {
            'name': 'Optimized Bollinger Bands',
            'strategy': BollingerBandsMomentum(
                'BTCUSDT', '1h',
                bb_period=18, bb_std=1.8, volume_threshold=1.2, momentum_period=8
            )
        },
        {
            'name': 'Enhanced MACD',
            'strategy': MACDStrategy(
                'BTCUSDT', '1h',
                fast_period=10, slow_period=22, signal_period=7, histogram_threshold=0.0
            )
        },
        {
            'name': 'Advanced Momentum',
            'strategy': AdvancedMomentumStrategy(
                'BTCUSDT', '1h',
                momentum_period=12, roc_period=8, volatility_period=16, min_momentum_threshold=0.008
            )
        }
    ]
    
    results = {}
    total_trades = 0
    
    print("\n🎯 Testing strategies...")
    print("-" * 60)
    
    for strategy_info in strategies:
        name = strategy_info['name']
        strategy = strategy_info['strategy']
        
        try:
            print(f"\n   🔬 Testing {name}...")
            
            # Run backtest
            start_time = time.perf_counter()
            result = backtester.run_backtest(strategy, data)
            execution_time = time.perf_counter() - start_time
            
            # Store results
            results[name] = result
            total_trades += result.total_trades
            
            # Display results
            print(f"      ✅ Return: {result.total_return:.2%}")
            print(f"      📊 Sharpe: {result.sharpe_ratio:.4f}")
            print(f"      📈 Trades: {result.total_trades}")
            print(f"      🎯 Win Rate: {result.win_rate:.1%}")
            print(f"      ⏱️ Time: {execution_time:.3f}s")
            
            if result.total_trades > 0:
                print(f"      💰 Avg Trade: {(result.total_return * 10000 / result.total_trades):.2f}")
            
        except Exception as e:
            print(f"      ❌ Failed: {e}")
            continue
    
    # Summary
    print(f"\n🏆 SUMMARY")
    print("=" * 60)
    print(f"   📊 Total Strategies Tested: {len(results)}")
    print(f"   🔄 Total Trades Generated: {total_trades}")
    print(f"   📈 Average Trades per Strategy: {total_trades / len(results) if results else 0:.1f}")
    
    # Find best performer
    if results:
        best_strategy = max(results.items(), key=lambda x: x[1].sharpe_ratio)
        print(f"\n🥇 Best Strategy: {best_strategy[0]}")
        print(f"   📊 Sharpe Ratio: {best_strategy[1].sharpe_ratio:.4f}")
        print(f"   💰 Total Return: {best_strategy[1].total_return:.2%}")
        print(f"   🔄 Trades: {best_strategy[1].total_trades}")
        
        # Performance comparison
        profitable_strategies = sum(1 for r in results.values() if r.total_return > 0)
        print(f"\n📈 Profitable Strategies: {profitable_strategies}/{len(results)}")
        
        if total_trades > 0:
            print("✅ SUCCESS: Strategies are generating trades!")
        else:
            print("⚠️ WARNING: No trades generated - may need further optimization")
    
    return results


def test_signal_generation():
    """Test signal generation for each strategy."""
    print("\n📡 TESTING SIGNAL GENERATION")
    print("=" * 60)
    
    # Generate test data
    data_manager = DataManager()
    data = data_manager._generate_demo_data('BTCUSDT', '1h', '2024-01-01', '2024-01-10')
    
    strategies = [
        MovingAverageCrossover('BTCUSDT', '1h', fast_period=8, slow_period=21),
        RSIMeanReversion('BTCUSDT', '1h', rsi_period=12, oversold_threshold=35, overbought_threshold=65),
        AdvancedMomentumStrategy('BTCUSDT', '1h', momentum_period=12, roc_period=8)
    ]
    
    for strategy in strategies:
        print(f"\n   🎯 Testing {strategy.name}...")
        
        # Initialize strategy
        strategy.initialize(data.head(50))
        
        # Generate signals for different data windows
        signals_generated = 0
        for i in range(60, len(data), 10):
            window_data = data.iloc[:i]
            signal = strategy.process_data(window_data)
            
            if signal.action != 'HOLD':
                signals_generated += 1
                print(f"      📊 Signal {signals_generated}: {signal.action} at ${signal.price:.2f} (confidence: {signal.confidence:.2f})")
        
        print(f"      ✅ Generated {signals_generated} signals from {(len(data) - 60) // 10} windows")


if __name__ == "__main__":
    print("🔍 IMPROVED STRATEGY TESTING SUITE")
    print("=" * 80)
    
    try:
        # Test improved strategies
        results = test_improved_strategies()
        
        # Test signal generation
        test_signal_generation()
        
        print("\n🎉 ALL TESTS COMPLETED!")
        print("✅ Enhanced strategies are ready for production")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
