"""
Setup script for Binance API configuration.
Helps users configure their API keys and test connection.
"""
import os
import sys
from pathlib import Path

def create_env_file():
    """Create .env file with user input."""
    
    print("🔧 BINANCE API SETUP")
    print("=" * 50)
    print("This script will help you configure your Binance API keys.")
    print("You can get your API keys from: https://www.binance.com/en/my/settings/api-management")
    print()
    
    # Check if .env already exists
    if os.path.exists('.env'):
        response = input("⚠️ .env file already exists. Overwrite? (y/N): ")
        if response.lower() != 'y':
            print("Setup cancelled.")
            return False
    
    # Get API credentials
    print("📝 Enter your Binance API credentials:")
    api_key = input("API Key: ").strip()
    api_secret = input("API Secret: ").strip()
    
    if not api_key or not api_secret:
        print("❌ API Key and Secret are required!")
        return False
    
    # Ask about testnet
    print("\n🧪 Do you want to use Binance Testnet?")
    print("   Testnet is recommended for testing without real money.")
    print("   Get testnet keys from: https://testnet.binance.vision/")
    use_testnet = input("Use testnet? (Y/n): ").strip().lower()
    testnet = use_testnet != 'n'
    
    # Create .env content
    env_content = f"""# Binance API Configuration
BINANCE_API_KEY={api_key}
BINANCE_API_SECRET={api_secret}
BINANCE_TESTNET={str(testnet).lower()}

# Trading Configuration
DEFAULT_SYMBOL=BTCUSDT
DEFAULT_INTERVAL=1h
MAX_CONCURRENT_STRATEGIES=10
BACKTEST_START_DATE=2020-01-01
BACKTEST_END_DATE=2024-01-01

# Performance Settings (optimized for compatibility)
ENABLE_JIT_COMPILATION=false
CACHE_ENABLED=false
PARALLEL_PROCESSING=false
MAX_WORKERS=4

# Risk Management
MAX_POSITION_SIZE=0.1
STOP_LOSS_PERCENTAGE=0.02
TAKE_PROFIT_PERCENTAGE=0.06
MAX_DRAWDOWN=0.15

# Database (local SQLite)
DATABASE_URL=sqlite:///trading_data.db

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/trading.log
"""
    
    # Write .env file
    try:
        with open('.env', 'w') as f:
            f.write(env_content)
        print("✅ .env file created successfully!")
        return True
    except Exception as e:
        print(f"❌ Error creating .env file: {e}")
        return False

def test_binance_connection():
    """Test Binance API connection."""
    
    print("\n🔍 Testing Binance API connection...")
    
    try:
        # Import after .env is created
        from dotenv import load_dotenv
        load_dotenv()
        
        from binance.client import Client
        
        api_key = os.getenv('BINANCE_API_KEY')
        api_secret = os.getenv('BINANCE_API_SECRET')
        testnet = os.getenv('BINANCE_TESTNET', 'true').lower() == 'true'
        
        if not api_key or not api_secret:
            print("❌ API credentials not found in .env file")
            return False
        
        # Create client
        client = Client(api_key, api_secret, testnet=testnet)
        
        # Test connection
        print("   📡 Testing API connection...")
        account_info = client.get_account()
        
        print("✅ Connection successful!")
        print(f"   Account Type: {account_info.get('accountType', 'Unknown')}")
        print(f"   Can Trade: {account_info.get('canTrade', False)}")
        print(f"   Can Withdraw: {account_info.get('canWithdraw', False)}")
        
        # Test market data
        print("   📊 Testing market data...")
        ticker = client.get_symbol_ticker(symbol="BTCUSDT")
        print(f"   BTCUSDT Price: ${float(ticker['price']):,.2f}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("   Install with: pip install python-binance python-dotenv")
        return False
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        print("   Please check your API keys and network connection.")
        return False

def install_dependencies():
    """Install required dependencies."""
    
    print("\n📦 Installing required dependencies...")
    
    dependencies = [
        "python-binance",
        "python-dotenv", 
        "numpy",
        "pandas",
        "loguru"
    ]
    
    try:
        import subprocess
        
        for dep in dependencies:
            print(f"   Installing {dep}...")
            result = subprocess.run([sys.executable, "-m", "pip", "install", dep], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"   ✅ {dep} installed")
            else:
                print(f"   ❌ Failed to install {dep}: {result.stderr}")
                return False
        
        print("✅ All dependencies installed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error installing dependencies: {e}")
        return False

def main():
    """Main setup function."""
    
    print("🚀 CRYPTOCURRENCY TRADING FRAMEWORK SETUP")
    print("=" * 60)
    print("This setup will configure your Binance API and install dependencies.")
    print()
    
    # Step 1: Install dependencies
    print("STEP 1: Install Dependencies")
    print("-" * 30)
    
    try:
        import binance
        import dotenv
        import numpy
        import pandas
        import loguru
        print("✅ All dependencies already installed!")
    except ImportError:
        if not install_dependencies():
            print("❌ Setup failed at dependency installation.")
            return False
    
    # Step 2: Configure API
    print("\nSTEP 2: Configure Binance API")
    print("-" * 30)
    
    if not create_env_file():
        print("❌ Setup failed at API configuration.")
        return False
    
    # Step 3: Test connection
    print("\nSTEP 3: Test Connection")
    print("-" * 30)
    
    if not test_binance_connection():
        print("⚠️ Connection test failed, but setup is complete.")
        print("   You can still use the framework with demo data.")
    
    # Step 4: Create directories
    print("\nSTEP 4: Create Directories")
    print("-" * 30)
    
    directories = ['logs', 'data', 'results']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"   ✅ Created {directory}/ directory")
    
    # Final instructions
    print("\n" + "=" * 60)
    print("🎉 SETUP COMPLETE!")
    print("=" * 60)
    print("Your trading framework is now configured and ready to use.")
    print()
    print("🚀 NEXT STEPS:")
    print("   1. Run the framework: python main.py")
    print("   2. Run evolution: python evolution_runner.py")
    print("   3. Run minimal version: python minimal_framework.py")
    print()
    print("📁 FILES CREATED:")
    print("   ✅ .env - Your API configuration")
    print("   ✅ logs/ - Log files directory")
    print("   ✅ data/ - Data cache directory")
    print("   ✅ results/ - Results directory")
    print()
    print("🔧 CONFIGURATION:")
    print(f"   API Keys: {'✅ Configured' if os.getenv('BINANCE_API_KEY') else '❌ Missing'}")
    print(f"   Testnet: {os.getenv('BINANCE_TESTNET', 'true')}")
    print(f"   Default Symbol: {os.getenv('DEFAULT_SYMBOL', 'BTCUSDT')}")
    print()
    print("⚠️ SECURITY NOTE:")
    print("   Keep your .env file secure and never share your API keys!")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
