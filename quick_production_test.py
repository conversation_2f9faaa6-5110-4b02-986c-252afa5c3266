"""
Quick Production Test - Verify Framework Functionality
======================================================

This script performs a quick test to verify all framework components
are working correctly in production environment.
"""

import os
import sys
import time
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from loguru import logger
from core.data_manager import DataManager
from core.backtester import VectorizedBacktester
from strategies.technical_strategies import MovingAverageCrossover
from utils.performance_tracker import PerformanceTracker


def test_data_manager():
    """Test data manager functionality."""
    logger.info("🔍 Testing Data Manager...")
    
    try:
        dm = DataManager()
        
        # Test demo data generation
        data = dm.get_historical_data("BTCUSDT", "1h", "2023-12-01", "2023-12-31")
        
        if not data.empty:
            logger.info(f"✅ Data Manager: Generated {len(data)} data points")
            logger.info(f"   Date range: {data.index[0]} to {data.index[-1]}")
            return True
        else:
            logger.error("❌ Data Manager: No data generated")
            return False
            
    except Exception as e:
        logger.error(f"❌ Data Manager Error: {e}")
        return False


def test_strategy():
    """Test strategy functionality."""
    logger.info("🔍 Testing Strategy...")
    
    try:
        # Create test data
        dm = DataManager()
        data = dm.get_historical_data("BTCUSDT", "1h", "2023-12-01", "2023-12-31")
        
        if data.empty:
            logger.error("❌ Strategy Test: No data available")
            return False
            
        # Test strategy
        strategy = MovingAverageCrossover("BTCUSDT", "1h", fast_period=10, slow_period=20)
        strategy.initialize(data)
        
        # Generate a signal
        signal = strategy.process_data(data)
        
        logger.info(f"✅ Strategy: Initialized with {len(data)} data points")
        logger.info(f"   Signal generated: {signal.action} (confidence: {signal.confidence:.2f})")
        return True
        
    except Exception as e:
        logger.error(f"❌ Strategy Error: {e}")
        return False


def test_backtester():
    """Test backtester functionality."""
    logger.info("🔍 Testing Backtester...")
    
    try:
        # Create test components
        dm = DataManager()
        data = dm.get_historical_data("BTCUSDT", "1h", "2023-12-01", "2023-12-31")
        
        if data.empty:
            logger.error("❌ Backtester Test: No data available")
            return False
            
        strategy = MovingAverageCrossover("BTCUSDT", "1h", fast_period=10, slow_period=20)
        backtester = VectorizedBacktester(initial_capital=10000.0)
        
        # Run backtest
        start_time = time.time()
        result = backtester.run_backtest(strategy, data)
        execution_time = time.time() - start_time
        
        logger.info(f"✅ Backtester: Completed in {execution_time:.2f}s")
        logger.info(f"   Total Return: {result.total_return:.2%}")
        logger.info(f"   Sharpe Ratio: {result.sharpe_ratio:.4f}")
        logger.info(f"   Total Trades: {result.total_trades}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Backtester Error: {e}")
        return False


def test_performance_tracker():
    """Test performance tracker functionality."""
    logger.info("🔍 Testing Performance Tracker...")
    
    try:
        tracker = PerformanceTracker()
        
        # Create dummy result for testing
        class DummyResult:
            def __init__(self):
                self.total_return = 0.15
                self.sharpe_ratio = 1.5
                self.max_drawdown = 0.05
                self.win_rate = 0.65
                self.total_trades = 50
                self.execution_time = 2.5
                
        dummy_result = DummyResult()
        dummy_params = {"fast_period": 10, "slow_period": 20}
        
        # Add test result
        tracker.add_result("TestStrategy", dummy_result, dummy_params)
        
        logger.info("✅ Performance Tracker: Successfully stored test result")
        return True
        
    except Exception as e:
        logger.error(f"❌ Performance Tracker Error: {e}")
        return False


def test_optimization():
    """Test basic optimization functionality."""
    logger.info("🔍 Testing Optimization (Quick)...")
    
    try:
        from core.optimizer import StrategyOptimizer
        
        dm = DataManager()
        data = dm.get_historical_data("BTCUSDT", "1h", "2023-12-01", "2023-12-31")
        
        if data.empty:
            logger.error("❌ Optimization Test: No data available")
            return False
            
        backtester = VectorizedBacktester(initial_capital=10000.0)
        optimizer = StrategyOptimizer(dm, backtester, optimization_metric='sharpe_ratio')
        
        # Quick optimization with minimal trials
        param_ranges = {
            'fast_period': (5, 15),
            'slow_period': (15, 25)
        }
        
        start_time = time.time()
        result = optimizer.optimize_strategy(
            MovingAverageCrossover,
            "BTCUSDT",
            "1h",
            param_ranges,
            "2023-12-01",
            "2023-12-31",
            method='bayesian',
            n_trials=3  # Quick test
        )
        execution_time = time.time() - start_time
        
        logger.info(f"✅ Optimization: Completed in {execution_time:.2f}s")
        logger.info(f"   Best Score: {result.best_score:.4f}")
        logger.info(f"   Best Parameters: {result.best_parameters}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Optimization Error: {e}")
        return False


def main():
    """Run quick production test."""
    logger.info("🚀 QUICK PRODUCTION TEST")
    logger.info("=" * 50)
    logger.info(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    start_time = time.time()
    
    # Run all tests
    tests = [
        ("Data Manager", test_data_manager),
        ("Strategy", test_strategy),
        ("Backtester", test_backtester),
        ("Performance Tracker", test_performance_tracker),
        ("Optimization", test_optimization)
    ]
    
    results = {}
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} Test ---")
        results[test_name] = test_func()
    
    # Summary
    total_time = time.time() - start_time
    passed = sum(results.values())
    total = len(results)
    
    logger.info("\n" + "=" * 50)
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 50)
    
    for test_name, passed_test in results.items():
        status = "✅ PASS" if passed_test else "❌ FAIL"
        logger.info(f"  {test_name}: {status}")
    
    logger.info(f"\n🎯 Overall: {passed}/{total} tests passed")
    logger.info(f"⏱️ Total execution time: {total_time:.2f} seconds")
    
    if passed == total:
        logger.info("🎉 ALL TESTS PASSED - Framework is production ready!")
    else:
        logger.info("⚠️ Some tests failed - Please check the issues above")
    
    logger.info("=" * 50)


if __name__ == "__main__":
    main()
