"""
Minimal self-contained cryptocurrency trading framework.
Works with just Python standard library + numpy/pandas.
"""
import sys
import os
import json
import sqlite3
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Try to import numpy and pandas, fallback to basic Python if not available
try:
    import numpy as np
    import pandas as pd
    NUMPY_AVAILABLE = True
    PANDAS_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    PANDAS_AVAILABLE = False
    np = None
    pd = None

# Simple configuration
class SimpleSettings:
    """Simple settings without external dependencies."""
    
    def __init__(self):
        # Load from environment or use defaults
        self.binance_api_key = os.getenv("BINANCE_API_KEY", "demo_key")
        self.binance_api_secret = os.getenv("BINANCE_API_SECRET", "demo_secret")
        self.binance_testnet = True
        
        # Trading settings
        self.default_symbol = "BTCUSDT"
        self.default_interval = "1h"
        self.max_position_size = 0.1
        self.stop_loss_percentage = 0.02
        self.take_profit_percentage = 0.06
        
        # Performance settings
        self.enable_jit_compilation = False  # Disabled for minimal version
        self.cache_enabled = False
        self.parallel_processing = False

# Simple signal class
class SimpleSignal:
    """Simple signal representation."""
    
    def __init__(self, timestamp, action, price, confidence=0.5, metadata=None):
        self.timestamp = timestamp
        self.action = action  # 'BUY', 'SELL', 'HOLD'
        self.price = price
        self.confidence = confidence
        self.metadata = metadata or {}

# Simple strategy base class
class SimpleStrategy:
    """Simple strategy base class."""
    
    def __init__(self, name, symbol, timeframe):
        self.name = name
        self.symbol = symbol
        self.timeframe = timeframe
        self.trades = []
        self.signals = []
        
    def generate_signal(self, data):
        """Override this method in subclasses."""
        return SimpleSignal(
            timestamp=datetime.now(),
            action='HOLD',
            price=data[-1] if data else 0,
            confidence=0.0
        )

# Simple moving average strategy
class SimpleMovingAverageStrategy(SimpleStrategy):
    """Simple moving average crossover strategy."""
    
    def __init__(self, symbol, timeframe, fast_period=10, slow_period=30):
        super().__init__(f"MA_{fast_period}_{slow_period}", symbol, timeframe)
        self.fast_period = fast_period
        self.slow_period = slow_period
    
    def simple_moving_average(self, data, period):
        """Calculate simple moving average."""
        if len(data) < period:
            return [0] * len(data)
        
        ma = []
        for i in range(len(data)):
            if i < period - 1:
                ma.append(0)
            else:
                ma.append(sum(data[i-period+1:i+1]) / period)
        return ma
    
    def generate_signal(self, prices):
        """Generate signal based on MA crossover."""
        if len(prices) < max(self.fast_period, self.slow_period) + 1:
            return SimpleSignal(
                timestamp=datetime.now(),
                action='HOLD',
                price=prices[-1] if prices else 0,
                confidence=0.0,
                metadata={'reason': 'insufficient_data'}
            )
        
        # Calculate moving averages
        fast_ma = self.simple_moving_average(prices, self.fast_period)
        slow_ma = self.simple_moving_average(prices, self.slow_period)
        
        # Current and previous values
        current_fast = fast_ma[-1]
        current_slow = slow_ma[-1]
        prev_fast = fast_ma[-2] if len(fast_ma) > 1 else current_fast
        prev_slow = slow_ma[-2] if len(slow_ma) > 1 else current_slow
        
        # Signal generation
        action = 'HOLD'
        confidence = 0.0
        
        # Bullish crossover
        if prev_fast <= prev_slow and current_fast > current_slow:
            action = 'BUY'
            confidence = 0.7
        # Bearish crossover
        elif prev_fast >= prev_slow and current_fast < current_slow:
            action = 'SELL'
            confidence = 0.7
        
        return SimpleSignal(
            timestamp=datetime.now(),
            action=action,
            price=prices[-1],
            confidence=confidence,
            metadata={
                'fast_ma': current_fast,
                'slow_ma': current_slow,
                'crossover': action != 'HOLD'
            }
        )

# Simple backtester
class SimpleBacktester:
    """Simple backtesting engine."""
    
    def __init__(self, initial_capital=10000):
        self.initial_capital = initial_capital
        self.commission = 0.001  # 0.1%
    
    def run_backtest(self, strategy, prices, timestamps=None):
        """Run simple backtest."""
        if not prices or len(prices) < 50:
            return {
                'error': 'Insufficient data',
                'total_return': 0,
                'total_trades': 0,
                'win_rate': 0
            }
        
        capital = self.initial_capital
        position = 0
        trades = []
        equity_curve = [capital]
        
        # Generate timestamps if not provided
        if timestamps is None:
            timestamps = [datetime.now() + timedelta(hours=i) for i in range(len(prices))]
        
        # Process each price point
        for i in range(50, len(prices)):  # Start after warmup period
            current_prices = prices[:i+1]
            signal = strategy.generate_signal(current_prices)
            
            current_price = prices[i]
            
            # Execute trades based on signals
            if signal.action == 'BUY' and position <= 0:
                # Buy signal
                if position < 0:
                    # Close short position
                    pnl = (position * -1) * (position - current_price)
                    capital += pnl
                    trades.append({
                        'type': 'close_short',
                        'price': current_price,
                        'pnl': pnl,
                        'timestamp': timestamps[i]
                    })
                
                # Open long position
                position_size = (capital * 0.1) / current_price  # 10% of capital
                position = position_size
                capital -= position_size * current_price * (1 + self.commission)
                
            elif signal.action == 'SELL' and position >= 0:
                # Sell signal
                if position > 0:
                    # Close long position
                    pnl = position * (current_price - (capital / position if position > 0 else current_price))
                    capital += position * current_price * (1 - self.commission)
                    trades.append({
                        'type': 'close_long',
                        'price': current_price,
                        'pnl': pnl,
                        'timestamp': timestamps[i]
                    })
                    position = 0
            
            # Update equity curve
            unrealized_pnl = 0
            if position > 0:
                unrealized_pnl = position * (current_price - (self.initial_capital - capital) / position)
            elif position < 0:
                unrealized_pnl = position * (current_price - (self.initial_capital - capital) / abs(position))
            
            equity_curve.append(capital + unrealized_pnl)
        
        # Calculate performance metrics
        total_return = (equity_curve[-1] - self.initial_capital) / self.initial_capital
        winning_trades = len([t for t in trades if t.get('pnl', 0) > 0])
        win_rate = winning_trades / len(trades) if trades else 0
        
        return {
            'initial_capital': self.initial_capital,
            'final_capital': equity_curve[-1],
            'total_return': total_return,
            'total_trades': len(trades),
            'winning_trades': winning_trades,
            'win_rate': win_rate,
            'trades': trades,
            'equity_curve': equity_curve
        }

# Sample data generator
def generate_sample_data(days=365, start_price=30000):
    """Generate sample price data."""
    if NUMPY_AVAILABLE:
        np.random.seed(42)
        returns = np.random.normal(0.0001, 0.02, days * 24)  # Hourly data
        prices = start_price * np.exp(np.cumsum(returns))
        return prices.tolist()
    else:
        # Fallback without numpy
        import random
        random.seed(42)
        prices = [start_price]
        for _ in range(days * 24 - 1):
            change = random.gauss(0.0001, 0.02)
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 1))  # Ensure positive prices
        return prices

# Main demonstration function
def run_minimal_demo():
    """Run minimal framework demonstration."""
    print("🚀 MINIMAL CRYPTOCURRENCY TRADING FRAMEWORK")
    print("=" * 60)
    print("This is a self-contained version that works with minimal dependencies")
    
    # Check dependencies
    print("\n📦 Checking dependencies...")
    print(f"   NumPy: {'✅ Available' if NUMPY_AVAILABLE else '❌ Not available (using fallback)'}")
    print(f"   Pandas: {'✅ Available' if PANDAS_AVAILABLE else '❌ Not available (using fallback)'}")
    
    # Generate sample data
    print("\n📊 Generating sample data...")
    prices = generate_sample_data(days=30)  # 30 days of hourly data
    print(f"   ✅ Generated {len(prices)} price points")
    print(f"   Price range: ${min(prices):.2f} - ${max(prices):.2f}")
    
    # Create strategy
    print("\n🎯 Creating strategy...")
    strategy = SimpleMovingAverageStrategy("BTCUSDT", "1h", fast_period=10, slow_period=30)
    print(f"   ✅ Created strategy: {strategy.name}")
    
    # Test signal generation
    print("\n📡 Testing signal generation...")
    signal = strategy.generate_signal(prices[-100:])  # Use last 100 prices
    print(f"   ✅ Generated signal: {signal.action} at ${signal.price:.2f}")
    print(f"   Confidence: {signal.confidence:.2f}")
    print(f"   Metadata: {signal.metadata}")
    
    # Run backtest
    print("\n🔬 Running backtest...")
    backtester = SimpleBacktester(initial_capital=10000)
    start_time = time.time()
    result = backtester.run_backtest(strategy, prices)
    execution_time = time.time() - start_time
    
    print(f"   ✅ Backtest completed in {execution_time:.4f} seconds")
    print(f"   Initial Capital: ${result['initial_capital']:,.2f}")
    print(f"   Final Capital: ${result['final_capital']:,.2f}")
    print(f"   Total Return: {result['total_return']:.2%}")
    print(f"   Total Trades: {result['total_trades']}")
    print(f"   Win Rate: {result['win_rate']:.2%}")
    
    # Show some trades
    if result['trades']:
        print(f"\n📋 Sample trades (showing first 3):")
        for i, trade in enumerate(result['trades'][:3]):
            print(f"   {i+1}. {trade['type']} at ${trade['price']:.2f} - P&L: ${trade.get('pnl', 0):.2f}")
    
    print("\n🎉 MINIMAL FRAMEWORK TEST COMPLETED!")
    print("\nThis demonstrates that the framework core logic is working.")
    print("For full functionality, install the complete dependencies:")
    print("   pip install numpy pandas python-binance loguru")
    
    return True

if __name__ == "__main__":
    try:
        success = run_minimal_demo()
        print(f"\nResult: {'SUCCESS' if success else 'FAILED'}")
    except Exception as e:
        print(f"\nError: {e}")
        import traceback
        traceback.print_exc()
