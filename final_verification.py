"""
Final Production Verification Script
====================================

This script performs final verification that the trading framework
is production-ready and all components are functioning correctly.
"""

import os
import sys
import sqlite3
from datetime import datetime
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from loguru import logger


def verify_framework_status():
    """Verify the current framework execution status."""
    logger.info("🔍 FINAL PRODUCTION VERIFICATION")
    logger.info("=" * 60)
    
    # Check if main program is running
    processes_running = check_running_processes()
    
    # Verify databases
    db_status = verify_databases()
    
    # Check log files
    log_status = verify_logs()
    
    # Verify configuration
    config_status = verify_configuration()
    
    # Generate final report
    generate_final_report(processes_running, db_status, log_status, config_status)


def check_running_processes():
    """Check if framework processes are running."""
    logger.info("📊 Checking Framework Execution Status...")
    
    # Check if performance database has recent activity
    try:
        if Path("performance_tracking.db").exists():
            conn = sqlite3.connect("performance_tracking.db")
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM strategy_results")
            result_count = cursor.fetchone()[0]
            conn.close()
            
            if result_count > 0:
                logger.info(f"✅ Framework Active: {result_count} strategy results recorded")
                return True
            else:
                logger.info("⚠️ Framework Status: No results yet (may be starting)")
                return False
        else:
            logger.info("⚠️ Performance database not found")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error checking processes: {e}")
        return False


def verify_databases():
    """Verify database integrity and content."""
    logger.info("\n📊 Database Verification...")
    
    databases = {
        "trading_data.db": "Historical trading data",
        "performance_tracking.db": "Strategy performance results"
    }
    
    status = {}
    
    for db_file, description in databases.items():
        if Path(db_file).exists():
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # Get table count
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                
                if tables:
                    logger.info(f"✅ {description}: {len(tables)} tables")
                    status[db_file] = True
                else:
                    logger.info(f"⚠️ {description}: Database empty")
                    status[db_file] = False
                    
                conn.close()
                
            except Exception as e:
                logger.error(f"❌ {description}: Error - {e}")
                status[db_file] = False
        else:
            logger.info(f"⚠️ {description}: File not found")
            status[db_file] = False
    
    return status


def verify_logs():
    """Verify log files and recent activity."""
    logger.info("\n📋 Log File Verification...")
    
    log_files = [
        "logs/trading.log",
        "logs/production_summary.log"
    ]
    
    status = {}
    
    for log_file in log_files:
        if Path(log_file).exists():
            try:
                # Check file size and modification time
                file_stat = Path(log_file).stat()
                size_mb = file_stat.st_size / (1024 * 1024)
                mod_time = datetime.fromtimestamp(file_stat.st_mtime)
                
                logger.info(f"✅ {log_file}: {size_mb:.2f}MB, modified {mod_time}")
                status[log_file] = True
                
            except Exception as e:
                logger.error(f"❌ {log_file}: Error - {e}")
                status[log_file] = False
        else:
            logger.info(f"⚠️ {log_file}: Not found")
            status[log_file] = False
    
    return status


def verify_configuration():
    """Verify configuration files and settings."""
    logger.info("\n⚙️ Configuration Verification...")
    
    config_files = {
        ".env": "Environment variables",
        "config/settings.py": "Framework settings",
        "requirements.txt": "Dependencies"
    }
    
    status = {}
    
    for config_file, description in config_files.items():
        if Path(config_file).exists():
            logger.info(f"✅ {description}: Found")
            status[config_file] = True
        else:
            logger.info(f"❌ {description}: Missing")
            status[config_file] = False
    
    return status


def check_optimization_results():
    """Check for optimization results in the database."""
    logger.info("\n🎯 Optimization Results Check...")
    
    try:
        if Path("performance_tracking.db").exists():
            conn = sqlite3.connect("performance_tracking.db")
            cursor = conn.cursor()
            
            # Check for recent results
            cursor.execute("""
                SELECT strategy_name, sharpe_ratio, total_return, timestamp 
                FROM strategy_results 
                ORDER BY timestamp DESC 
                LIMIT 5
            """)
            
            results = cursor.fetchall()
            
            if results:
                logger.info("🏆 Recent Optimization Results:")
                for result in results:
                    name, sharpe, return_val, timestamp = result
                    logger.info(f"   {name}: Sharpe {sharpe:.4f}, Return {return_val:.2%}")
            else:
                logger.info("⚠️ No optimization results found yet")
            
            conn.close()
            
    except Exception as e:
        logger.error(f"❌ Error checking optimization results: {e}")


def generate_final_report(processes, databases, logs, config):
    """Generate final verification report."""
    logger.info("\n" + "=" * 60)
    logger.info("🎯 FINAL VERIFICATION REPORT")
    logger.info("=" * 60)
    
    # Calculate overall status
    total_checks = 0
    passed_checks = 0
    
    categories = {
        "Framework Execution": processes,
        "Database Systems": all(databases.values()) if databases else False,
        "Logging System": all(logs.values()) if logs else False,
        "Configuration": all(config.values()) if config else False
    }
    
    for category, status in categories.items():
        total_checks += 1
        if status:
            passed_checks += 1
            logger.info(f"✅ {category}: OPERATIONAL")
        else:
            logger.info(f"❌ {category}: NEEDS ATTENTION")
    
    # Overall status
    success_rate = (passed_checks / total_checks) * 100
    
    logger.info(f"\n📊 OVERALL STATUS: {passed_checks}/{total_checks} checks passed ({success_rate:.1f}%)")
    
    if success_rate >= 75:
        logger.info("🎉 FRAMEWORK STATUS: PRODUCTION READY!")
        logger.info("✅ System is operational and ready for live trading")
    elif success_rate >= 50:
        logger.info("⚠️ FRAMEWORK STATUS: MOSTLY READY")
        logger.info("🔧 Minor issues detected - review and fix before production")
    else:
        logger.info("❌ FRAMEWORK STATUS: NEEDS WORK")
        logger.info("🛠️ Significant issues detected - address before deployment")
    
    # Check for live optimization
    check_optimization_results()
    
    logger.info("\n💡 RECOMMENDATIONS:")
    logger.info("   1. Monitor the main.py execution for completion")
    logger.info("   2. Review optimization results when available")
    logger.info("   3. Implement best-performing strategies")
    logger.info("   4. Set up production monitoring")
    logger.info("   5. Schedule regular performance reviews")
    
    logger.info(f"\n📅 Verification completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 60)


def main():
    """Main verification function."""
    # Setup logging
    logger.add(
        "logs/verification.log",
        level="INFO",
        rotation="1 day",
        retention="7 days",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
    )
    
    verify_framework_status()


if __name__ == "__main__":
    main()
