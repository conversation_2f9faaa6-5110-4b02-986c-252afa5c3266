"""
One-click startup script for the cryptocurrency trading framework.
Automatically configures and runs the framework with real Binance data.
"""
import sys
import os
import subprocess
import time
from pathlib import Path


def print_banner():
    """Print framework banner."""
    print("🚀" + "="*78 + "🚀")
    print("🚀" + " "*20 + "CRYPTOCURRENCY TRADING FRAMEWORK" + " "*25 + "🚀")
    print("🚀" + "="*78 + "🚀")
    print("🚀  ⚡ Ultra-fast execution (< 1ms signal generation)              🚀")
    print("🚀  🛡️  Real-world robustness (walk-forward analysis)              🚀")
    print("🚀  🧬 Iterative improvement (genetic optimization)               🚀")
    print("🚀  📊 Comprehensive metrics (15+ performance indicators)         🚀")
    print("🚀  🎯 Automated execution with real Binance data                 🚀")
    print("🚀" + "="*78 + "🚀")


def check_python_version():
    """Check if Python version is compatible."""
    print("🔍 Checking Python version...")

    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python {version.major}.{version.minor} detected")
        print("   This framework requires Python 3.8 or higher")
        print("   Please upgrade your Python installation")
        return False

    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
    return True


def install_dependencies():
    """Install required dependencies."""
    print("\n📦 Installing dependencies...")

    try:
        # Check if requirements.txt exists
        if not os.path.exists('requirements.txt'):
            print("❌ requirements.txt not found!")
            return False

        # Install core dependencies first
        print("   Installing core packages from requirements.txt...")
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ], capture_output=True, text=True, timeout=300)

        if result.returncode == 0:
            print("✅ Core dependencies installed successfully")

            # Try to install optional dependencies
            if os.path.exists('requirements-optional.txt'):
                print("   Installing optional packages (may fail on some systems)...")
                optional_result = subprocess.run([
                    sys.executable, '-m', 'pip', 'install', '-r', 'requirements-optional.txt'
                ], capture_output=True, text=True, timeout=300)

                if optional_result.returncode == 0:
                    print("✅ Optional dependencies installed successfully")
                else:
                    print("⚠️ Some optional dependencies failed to install (framework will still work)")
                    print("   You can install them manually later if needed")

            return True
        else:
            print(f"❌ Failed to install core dependencies:")
            print(f"   {result.stderr}")
            print("   Trying to continue anyway...")
            return False

    except subprocess.TimeoutExpired:
        print("❌ Installation timed out (5 minutes)")
        print("   You can install manually: pip install -r requirements.txt")
        return False
    except Exception as e:
        print(f"❌ Error installing dependencies: {e}")
        print("   You can install manually: pip install -r requirements.txt")
        return False


def check_api_credentials():
    """Check and configure API credentials."""
    print("\n🔑 Checking API credentials...")

    env_file = Path('.env')

    if not env_file.exists():
        print("❌ .env file not found!")
        print("   Creating .env file with template...")
        create_env_template()
        return False

    # Read .env file
    try:
        with open('.env', 'r') as f:
            content = f.read()

        # Check if credentials are configured
        if 'your_binance_api_key_here' in content or 'your_binance_secret_key_here' in content:
            print("⚠️  API credentials not configured!")
            print("   Please update the .env file with your Binance API credentials")
            print("   You can get them from: https://www.binance.com/en/my/settings/api-management")
            return False

        # Check if credentials exist
        lines = content.split('\n')
        api_key = None
        api_secret = None

        for line in lines:
            if line.startswith('BINANCE_API_KEY='):
                api_key = line.split('=', 1)[1].strip()
            elif line.startswith('BINANCE_API_SECRET='):
                api_secret = line.split('=', 1)[1].strip()

        if not api_key or not api_secret or len(api_key) < 10 or len(api_secret) < 10:
            print("⚠️  Invalid API credentials in .env file")
            return False

        print("✅ API credentials configured")
        return True

    except Exception as e:
        print(f"❌ Error reading .env file: {e}")
        return False


def create_env_template():
    """Create .env template file."""
    template = """# Binance API Configuration
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_API_SECRET=your_binance_secret_key_here
BINANCE_TESTNET=True

# Database Configuration
DATABASE_URL=sqlite:///trading_data.db
REDIS_URL=redis://localhost:6379/0

# Trading Configuration
DEFAULT_SYMBOL=BTCUSDT
DEFAULT_INTERVAL=1h
MAX_CONCURRENT_STRATEGIES=10
BACKTEST_START_DATE=2020-01-01
BACKTEST_END_DATE=2024-01-01

# Performance Configuration
ENABLE_JIT_COMPILATION=True
CACHE_ENABLED=True
PARALLEL_PROCESSING=True
MAX_WORKERS=8

# Risk Management
MAX_POSITION_SIZE=0.1
STOP_LOSS_PERCENTAGE=0.02
TAKE_PROFIT_PERCENTAGE=0.06
MAX_DRAWDOWN=0.15

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/trading.log
"""

    with open('.env', 'w') as f:
        f.write(template)

    print("📝 Created .env template file")


def test_framework():
    """Test framework functionality."""
    print("\n🧪 Testing framework...")

    try:
        # Test imports
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))

        from config.settings import settings
        print("   ✅ Configuration loaded")

        from core.data_manager import DataManager
        print("   ✅ Data manager imported")

        from strategies.technical_strategies import MovingAverageCrossover
        print("   ✅ Strategies imported")

        # Test strategy creation
        strategy = MovingAverageCrossover("BTCUSDT", "1h")
        print("   ✅ Strategy creation successful")

        print("✅ Framework test passed")
        return True

    except Exception as e:
        print(f"❌ Framework test failed: {e}")
        return False


def create_directories():
    """Create necessary directories."""
    print("\n📁 Creating directories...")

    directories = ['logs', 'data', 'results']

    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"   ✅ {directory}/")

    print("✅ Directories created")


def run_framework():
    """Run the trading framework."""
    print("\n🚀 Starting Trading Framework...")
    print("=" * 60)

    # Choose execution mode based on API credentials
    has_credentials = check_api_credentials()

    if has_credentials:
        print("🎯 Running with REAL Binance data")
        print("   This will test multiple strategies across different symbols and timeframes")
        print("   Estimated execution time: 10-30 minutes")
        print("\n⏳ Starting automated analysis...")

        try:
            # Run automated analysis
            import auto_run
            success = auto_run.main()

            if success:
                print("\n🎉 Analysis completed successfully!")
                print("📊 Check the logs/ directory for detailed results")
            else:
                print("\n⚠️ Analysis completed with some issues")

        except Exception as e:
            print(f"\n❌ Error running automated analysis: {e}")
            print("   Falling back to example mode...")
            run_examples()
    else:
        print("🎯 Running in DEMO mode (no API credentials)")
        print("   This will demonstrate framework capabilities with sample data")
        run_examples()


def run_examples():
    """Run example demonstrations."""
    try:
        import run_example
        run_example.main()
    except Exception as e:
        print(f"❌ Error running examples: {e}")


def main():
    """Main startup function."""
    print_banner()

    # Step 1: Check Python version
    if not check_python_version():
        input("\nPress Enter to exit...")
        return False

    # Step 2: Install dependencies
    print("\n" + "="*60)
    install_deps = True  # Auto-install dependencies

    if install_deps:
        if not install_dependencies():
            print("\n⚠️ Dependency installation failed")
            print("   You can try installing manually: pip install -r requirements.txt")
            input("\nPress Enter to continue anyway...")

    # Step 3: Create directories
    create_directories()

    # Step 4: Test framework
    if not test_framework():
        print("\n⚠️ Framework test failed - continuing anyway...")

    # Step 5: Run framework
    print("\n" + "="*60)
    print("🎯 READY TO START!")
    print("="*60)

    try:
        run_framework()
    except KeyboardInterrupt:
        print("\n\n⚠️ Execution interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()

    print("\n" + "="*60)
    print("🏁 Framework execution completed!")
    print("="*60)
    print("📚 Next steps:")
    print("   1. Check logs/ directory for detailed results")
    print("   2. Review generated strategy configurations")
    print("   3. Customize strategies in strategies/ directory")
    print("   4. Run 'python main.py' for custom analysis")

    return True


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
        sys.exit(0)
